# Rewards Integration Guide

This guide explains how to integrate the new rewards functionality into your POS system, including displaying customer points and allowing customers to avail rewards with zero price.

## Overview

The rewards system now includes:
1. **Customer Points Display** - Shows current points when customer details are fetched
2. **Avail Rewards Button** - Allows customers to browse and select reward items
3. **Zero Price Cart Addition** - Adds reward items to cart with zero price
4. **Rewards Avail Screen** - Dedicated screen for browsing and selecting rewards

## Key Components

### 1. Updated RewardsScreen
- Shows customer points when customer details are fetched
- Displays "Avail Rewards" button (enabled only when customer has points)
- Integrates with cart functionality

### 2. New RewardsAvailScreen
- Shows available reward items based on customer points
- Displays which items customer can afford
- Allows adding reward items to cart with zero price
- Shows "FREE" label for reward items

### 3. RewardsCartHelper
- Utility class for adding reward items to cart
- Handles conversion from RewardItem to StoreItem with zero price
- Provides both automatic and manual cart addition methods

## Integration Steps

### Step 1: Update Your Activity/Fragment

```kotlin
// In your main activity or fragment where you show the RewardsScreen
@Composable
fun YourMainScreen() {
    val productsViewModel: ProductsScreenViewModel = viewModel()
    val rewardsViewModel: RewardsViewModel = viewModel()
    
    RewardsScreen(
        viewModel = rewardsViewModel,
        onBackClick = { /* handle back */ },
        onAddToCart = { rewardItem ->
            // Add reward item to cart with zero price
            val state = productsViewModel.collectAsState().value
            RewardsCartHelper.addRewardItemToCart(
                rewardItem = rewardItem,
                productsViewModel = productsViewModel,
                state = state,
                quantity = 1
            )
            
            // Show success message
            showToast("Reward item added to cart - FREE!")
        }
    )
}
```

### Step 2: Handle Cart Integration

#### Option A: Using RewardsCartHelper (Recommended)
```kotlin
// Automatic integration with ProductsScreenViewModel
RewardsCartHelper.addRewardItemToCart(
    rewardItem = rewardItem,
    productsViewModel = productsViewModel,
    state = state,
    quantity = 1
)
```

#### Option B: Manual Cart Management
```kotlin
// Manual cart management
val updatedOrder = RewardsCartHelper.addRewardItemToCartManually(
    rewardItem = rewardItem,
    currentOrder = currentOrder,
    quantity = 1
)
// Update your order state
updateOrder(updatedOrder)
```

### Step 3: Update Your Navigation

```kotlin
// In your navigation setup
when (destination) {
    "rewards" -> {
        RewardsScreen(
            viewModel = rewardsViewModel,
            onBackClick = { navController.popBackStack() },
            onAddToCart = { rewardItem ->
                // Handle cart addition
                addRewardToCart(rewardItem)
                // Optionally navigate to cart
                navController.navigate("cart")
            }
        )
    }
}
```

## User Flow

### 1. Customer Search
1. User enters customer ID in RewardsScreen
2. System fetches customer details using `getAllCustomers()`
3. System automatically fetches customer points using `getUserPoints()`

### 2. Points Display
1. Customer points are displayed in a green card
2. "Avail Rewards" button is enabled only if customer has points > 0
3. "Add Points" button remains available for adding more points

### 3. Rewards Browsing
1. User clicks "Avail Rewards" button
2. System calls `getRewardsOverview()` API
3. RewardsAvailScreen shows available reward items
4. Items customer can't afford are grayed out

### 4. Reward Selection
1. Customer selects a reward item they can afford
2. Item is added to cart with zero price
3. Success message is shown
4. Customer can continue shopping or proceed to checkout

## API Integration

### Customer Points
```kotlin
// Automatically called when customer is found
LaunchedEffect(customer.id) {
    customer.id?.let { userId ->
        viewModel.getUserPoints(userId)
    }
}
```

### Rewards Overview
```kotlin
// Called when "Avail Rewards" is clicked
LaunchedEffect(customer.id) {
    customer.id?.let { userId ->
        viewModel.getRewardsOverview(userId)
    }
}
```

## UI Features

### Customer Points Card
- Green themed card showing current points
- Loading indicator while fetching points
- Displays "0 pts" if no points or loading fails

### Avail Rewards Button
- Blue themed button
- Disabled when customer has no points
- Shows "Avail Rewards" text

### Reward Items Display
- Product image, name, and description
- Points required badge
- "FREE" button for affordable items
- "Not enough points" message for unaffordable items

### Zero Price Cart Items
- Reward items added with price = 0.0
- Bill amount = 0.0
- Tax = 0.0
- Notes = "Reward Item - FREE"
- Category = "Rewards"

## Testing

### Using RewardsTestActivity
1. Run `RewardsTestActivity`
2. Enter User ID: 942, Business ID: 92
3. Click "Test Full Rewards Screen"
4. Enter customer ID and search
5. View points and click "Avail Rewards"
6. Select reward items to add to cart

### Test Data
- User ID: 942
- Business ID: 92
- Expected Points: 100.0
- Available Rewards: 4 items with different point requirements

## Error Handling

### No Points
- "Avail Rewards" button is disabled
- Message: "Customer has no points available"

### API Failures
- Loading indicators with error messages
- Graceful fallback to "Failed to load" states
- Retry mechanisms where appropriate

### Cart Integration Errors
- Toast messages for successful additions
- Error handling for cart addition failures
- Validation for duplicate reward items

## Customization

### Styling
- Update `ThemeGreen` and other colors in the files
- Modify card shapes and spacing
- Customize button styles and text

### Business Logic
- Modify point requirements logic
- Add quantity limits for reward items
- Implement reward expiration dates
- Add reward categories or filters

### Cart Behavior
- Customize how reward items appear in cart
- Add special handling for reward item modifications
- Implement reward item removal logic

## Best Practices

1. **Always validate customer points** before showing avail rewards option
2. **Handle network failures gracefully** with proper error messages
3. **Show loading states** during API calls
4. **Provide clear feedback** when items are added to cart
5. **Validate reward eligibility** before cart addition
6. **Update points after reward redemption** (if implementing point deduction)

This integration provides a complete rewards system that seamlessly integrates with your existing POS cart functionality while maintaining zero pricing for reward items.
