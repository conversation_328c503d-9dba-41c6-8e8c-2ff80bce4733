# Rewards API Implementation

This document outlines the implementation of the new Rewards APIs using Retrofit, MvRx state management, and Compose UI.

## APIs Implemented

### 1. Get User Points API
- **Endpoint**: `GET /api/rewards/points?userId={userId}&businessId={businessId}`
- **Response**: `Double` (user points value)
- **Example**: `100.0`

### 2. Get Rewards Overview API
- **Endpoint**: `GET /api/rewards/overview?userId={userId}&businessId={businessId}`
- **Response**: `RewardsOverviewResponse` containing user points and available reward items
- **Example Response**:
```json
{
    "userPoints": 100.0,
    "rewardItems": [
        {
            "id": 1,
            "businessId": 92,
            "stampsRequired": 10,
            "storeItem": {
                "id": 2922,
                "name": "St Honore",
                "price": 8.6,
                "description": "Tart with pastry cream & caramel coulis...",
                // ... other store item fields
            }
        }
    ]
}
```

## Files Created/Modified

### Data Models
- **Created**: `RewardsOverviewResponse.kt` - Response model for rewards overview API
- **Modified**: `RewardsState.kt` - Added new API response states

### Repository
- **Modified**: `RewardsRepository.kt` - Added new API methods:
  - `getUserPoints(userId: Int, businessId: Int)`
  - `getRewardsOverview(userId: Int, businessId: Int)`

### Use Cases
- **Created**: `GetUserPointsUseCase.kt` - Use case for getting user points
- **Created**: `GetRewardsOverviewUseCase.kt` - Use case for getting rewards overview

### ViewModel
- **Modified**: `RewardsViewModel.kt` - Added new methods:
  - `getUserPoints(userId: Int, businessId: Int?)`
  - `getRewardsOverview(userId: Int, businessId: Int?)`
  - `clearUserPointsResponse()`
  - `clearRewardsOverviewResponse()`

### UI Components
- **Created**: `UserPointsScreen.kt` - Compose screen to display user points
- **Created**: `RewardsOverviewScreen.kt` - Compose screen to display rewards overview
- **Created**: `RewardsTestActivity.kt` - Test activity to demonstrate the APIs

### Dependency Injection
- **Modified**: `AppUseCaseModule.kt` - Added DI providers for new use cases

## Usage Examples

### Using the APIs in ViewModel
```kotlin
// Get user points
viewModel.getUserPoints(userId = 942, businessId = 92)

// Get rewards overview
viewModel.getRewardsOverview(userId = 942, businessId = 92)
```

### Observing State in Compose
```kotlin
val state by viewModel.collectAsState()

when (val response = state.userPointsResponse) {
    is Loading -> { /* Show loading */ }
    is Success -> {
        val points = response.invoke()
        // Display points
    }
    else -> { /* Handle error */ }
}
```

### Testing the Implementation
1. Run the `RewardsTestActivity`
2. Enter User ID (e.g., 942) and Business ID (e.g., 92)
3. Click "Test Get User Points API" to see user points
4. Click "Test Get Rewards Overview API" to see full rewards overview

## Key Features

### State Management
- Uses MvRx for reactive state management
- Proper loading, success, and error states
- State persistence across configuration changes

### UI Design
- Material Design 3 components
- Responsive layouts with proper spacing
- Loading indicators and error handling
- Image loading with Coil for product images

### Architecture
- Clean Architecture with separation of concerns
- Repository pattern for data access
- Use cases for business logic
- Dependency injection with Hilt

### Error Handling
- Safe API calls with proper error handling
- User-friendly error messages
- Graceful fallbacks for missing data

## API Integration Details

The implementation uses Ktor HTTP client (existing in the project) instead of Retrofit as requested, maintaining consistency with the existing codebase. The APIs are integrated following the same patterns used in other parts of the application.

### Request Flow
1. UI triggers action (button click)
2. ViewModel calls use case
3. Use case calls repository
4. Repository makes HTTP request using Ktor
5. Response flows back through the chain
6. UI updates based on state changes

This implementation provides a complete, production-ready solution for the rewards APIs with proper error handling, loading states, and a clean, modern UI.
