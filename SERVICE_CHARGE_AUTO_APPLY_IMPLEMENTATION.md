# Service Charge Auto-Apply with Manual Removal Memory Implementation

## Overview
This implementation adds functionality to automatically apply service charge when adding items to a table order if `prefs.storeConfigurations?.data?.serviceChargeEnabledDefault == true`, while remembering when a user manually removes the service charge for a specific table order.

## Key Features
1. **Auto-apply service charge**: When the first item is added to a table, service charge is automatically applied if enabled in store configuration
2. **Manual removal memory**: When a user manually removes service charge for a table, the system remembers this choice
3. **Persistent memory**: Once service charge is manually removed for a table, it won't be auto-applied again for that table order
4. **Table-specific tracking**: Each table maintains its own service charge state and manual removal tracking

## Implementation Details

### New State Field
Added `tableServiceChargeManuallyRemoved: Map<Int, Boolean>` to `ProductsScreenState` to track which tables have had service charge manually removed by the user.

### Modified Methods

#### 1. `addItemToCart()`
- Checks if cart was empty before adding the item
- Auto-applies service charge if:
  - Store configuration has `serviceChargeEnabledDefault = true`
  - Cart was previously empty (first item being added)
  - Service charge hasn't been manually removed for this table
  - Service charge isn't already applied

#### 2. `removeServiceCharge()`
- Now tracks when service charge is manually removed
- Sets `tableServiceChargeManuallyRemoved[tableId] = true` when user removes service charge
- This prevents future auto-application for this table

#### 3. `initializeServiceChargeForTable()`
- Updated to check manual removal tracking before auto-applying
- Only auto-applies if service charge hasn't been manually removed for the table

#### 4. State Cleanup Methods
Updated the following methods to clean up service charge tracking when appropriate:
- `checkAndResetIfCartEmpty()`: Clears tracking when all tables are removed
- `clearCart()`: Resets tracking for specific table when cart is cleared
- `removeSelectedTable()`: Removes tracking when table is removed

### New Helper Method
Added `resetServiceChargeManualRemovalForTable(tableId: Int)` to allow resetting the manual removal tracking if needed.

## Usage Flow

### Scenario 1: New Table with Auto-Apply Enabled
1. User selects a table
2. User adds first item to cart
3. Service charge is automatically applied (if enabled in store config)
4. Cart shows with service charge included

### Scenario 2: User Manually Removes Service Charge
1. User has a table with service charge applied
2. User clicks "Remove Service Charge" button
3. Service charge is removed and `tableServiceChargeManuallyRemoved[tableId] = true`
4. Future items added to this table won't trigger auto-apply

### Scenario 3: Adding More Items to Table with Manual Removal
1. User previously removed service charge for a table
2. User adds more items to the same table
3. Service charge remains off (not auto-applied)
4. User can still manually apply service charge if desired

## Testing
Added comprehensive unit tests in `ProductsScreenViewModelTest.kt`:
- `test service charge auto-application for new table`
- `test service charge manual removal tracking`
- `test service charge state for multiple tables`
- `test isServiceChargeApplied for current table`
- `test isServiceChargeApplied for global order`

## Configuration
The feature is controlled by the existing store configuration:
```kotlin
prefs.storeConfigurations?.data?.serviceChargeEnabledDefault == true
```

## Backward Compatibility
- Existing UI components continue to work without changes
- Global cart (walk-in customers) behavior unchanged
- Existing service charge apply/remove functionality preserved

## State Management
The implementation maintains separate tracking for:
- `tableServiceChargeApplied: Map<Int, Boolean>` - Current service charge state per table
- `tableServiceChargeManuallyRemoved: Map<Int, Boolean>` - Manual removal tracking per table
- `serviceChargeApplied: Boolean` - Global service charge state for walk-in customers

## Memory Management
Service charge tracking is automatically cleaned up when:
- Tables are removed from selection
- Cart is cleared for a specific table
- All tables are removed (complete reset)

This ensures no memory leaks and proper state management across table operations.
