# Table-Based Course Status Implementation

## Overview
This document outlines the implementation of table-based course status management, where each table maintains its own independent course statuses and active course tracking.

## Problem Statement
Previously, course statuses were global, meaning:
- All tables shared the same course statuses
- Switching tables would show the same status for all tables
- Removing a table didn't clean up its course statuses

The new implementation provides:
- **Table-Specific Statuses**: Each table has its own course statuses
- **Independent Tracking**: Tables don't affect each other's statuses
- **Automatic Cleanup**: Removing tables cleans up their statuses
- **Seamless Switching**: Switching tables shows correct statuses

## Data Structure Changes

### Previous Structure (Global)
```kotlin
val courseStatuses: Map<String, CourseStatus> = emptyMap()
val currentActiveCourse: String? = null
```

### New Structure (Table-Based + Global)
```kotlin
val courseStatuses: Map<String, CourseStatus> = emptyMap() // Global (walk-in)
val tableCourseStatuses: Map<Int, Map<String, CourseStatus>> = emptyMap() // Table-specific
val currentActiveCourse: String? = null // Global active course (walk-in)
val tableActiveCourses: Map<Int, String> = emptyMap() // Table-specific active courses
```

## Implementation Details

### 1. State Structure

#### Table Course Statuses
```kotlin
val tableCourseStatuses: Map<Int, Map<String, CourseStatus>>
// Structure: Map<TableId, Map<CourseId, CourseStatus>>
// Example: {
//   101 -> {"course_starters" -> PREPARING, "course_mains" -> GO},
//   102 -> {"course_starters" -> COMPLETE, "course_desserts" -> PREPARING}
// }
```

#### Table Active Courses
```kotlin
val tableActiveCourses: Map<Int, String>
// Structure: Map<TableId, CourseId>
// Example: {
//   101 -> "course_mains",
//   102 -> "course_desserts"
// }
```

### 2. Core Functions

#### Course Status Management
```kotlin
fun updateCourseStatus(courseId: String, status: CourseStatus)
fun getCourseStatus(courseId: String): CourseStatus
fun getCurrentCourseStatuses(): Map<String, CourseStatus>
```

#### Table Management
```kotlin
fun onTableSelected(tableId: Int) // Initialize course statuses for new table
fun onTableRemoved(tableId: Int) // Clean up course statuses for removed table
fun removeTableCourseStatuses(tableId: Int) // Remove specific table statuses
fun clearAllTableCourseStatuses() // Clear all table statuses
fun resetCurrentTableCourseStatuses() // Reset current table statuses
```

#### Active Course Management
```kotlin
fun initializeActiveCourse() // Initialize active course for current context
fun moveToNextCourse(currentCourseId: String) // Move to next course in sequence
fun shouldShowGoButton(courseId: String): Boolean // Check if course should show Go button
```

## Functionality

### 1. Table Selection
When a table is selected:
```kotlin
fun onTableSelected(tableId: Int) {
    // Initialize course statuses for the table if it's new
    if (!tableActiveCourses.containsKey(tableId)) {
        tableActiveCourses[tableId] = "course_starters"
    }
}
```

### 2. Table Switching
When switching between tables:
- **Previous Table**: Statuses are preserved
- **New Table**: Shows its own statuses or defaults to GO
- **Active Course**: Each table remembers its active course

### 3. Table Removal
When a table is removed:
```kotlin
fun onTableRemoved(tableId: Int) {
    // Remove all course statuses for this table
    tableCourseStatuses.remove(tableId)
    tableActiveCourses.remove(tableId)
}
```

### 4. Course Status Updates
```kotlin
fun updateCourseStatus(courseId: String, status: CourseStatus) {
    val currentTableId = getCurrentTableId()
    if (currentTableId != null) {
        // Update table-specific status
        tableCourseStatuses[currentTableId][courseId] = status
    } else {
        // Update global status (walk-in)
        courseStatuses[courseId] = status
    }
}
```

## Usage Scenarios

### 1. Multi-Table Restaurant
```
Table 101: Starters(PREPARING), Mains(GO), Desserts(GO)
Table 102: Starters(COMPLETE), Mains(PREPARING), Desserts(GO)
Table 103: Starters(GO), Mains(GO), Desserts(GO)
```

### 2. Table Switching
```
User selects Table 101 → Shows: Starters(PREPARING), Mains(GO)
User selects Table 102 → Shows: Starters(COMPLETE), Mains(PREPARING)
User selects Table 103 → Shows: All courses(GO) - fresh table
```

### 3. Walk-in Customers
```
No table selected → Uses global course statuses
Independent from table statuses
```

## Benefits

### 1. Independent Table Management
- ✅ Each table has its own course progression
- ✅ Tables don't interfere with each other
- ✅ Staff can work on multiple tables simultaneously

### 2. Accurate Status Tracking
- ✅ Course statuses reflect actual table state
- ✅ No confusion between different tables
- ✅ Proper workflow management per table

### 3. Memory Management
- ✅ Automatic cleanup when tables are removed
- ✅ No memory leaks from orphaned statuses
- ✅ Efficient storage structure

### 4. Seamless UX
- ✅ Switching tables shows correct statuses immediately
- ✅ No manual status synchronization needed
- ✅ Intuitive behavior for restaurant staff

## API Integration

### Course Notification Logic
```kotlin
fun sendCoursesNotificationForCourse(courseId: String) {
    val currentTableId = getCurrentTableId()
    
    // Use appropriate cart items based on table selection
    val courseCartItems = if (currentTableId != null) {
        // Table mode: use table-specific cart items
        cartItemsWithCourses.filter { /* table-specific logic */ }
    } else {
        // Walk-in mode: use global cart items
        globalCartItemsWithCourses.filter { /* global logic */ }
    }
    
    // Update status for current table/global context
    updateCourseStatus(courseId, CourseStatus.PREPARING)
}
```

## Testing

### Test Coverage

#### 1. Table-Based Status Tests
```kotlin
@Test
fun `test table-based course statuses`()
@Test
fun `test table active courses`()
@Test
fun `test table removal logic`()
```

#### 2. State Management Tests
```kotlin
@Test
fun `test course status updates per table`()
@Test
fun `test table switching preserves statuses`()
@Test
fun `test global vs table status isolation`()
```

#### 3. Edge Case Tests
```kotlin
@Test
fun `test new table initialization`()
@Test
fun `test table removal cleanup`()
@Test
fun `test walk-in mode independence`()
```

## Implementation Flow

### 1. Table Selection
```
User selects Table 101
↓
onTableSelected(101) called
↓
Initialize course statuses if new table
↓
UI shows table-specific statuses
```

### 2. Course Status Update
```
User clicks Go on Starters for Table 101
↓
updateCourseStatus("course_starters", PREPARING)
↓
tableCourseStatuses[101]["course_starters"] = PREPARING
↓
UI updates to show "Preparing" for Starters on Table 101
```

### 3. Table Switching
```
User switches from Table 101 to Table 102
↓
UI automatically shows Table 102's course statuses
↓
Table 101's statuses preserved in memory
↓
Independent status tracking maintained
```

### 4. Table Removal
```
User removes Table 101
↓
onTableRemoved(101) called
↓
tableCourseStatuses.remove(101)
↓
tableActiveCourses.remove(101)
↓
Memory cleaned up, no orphaned data
```

## Files Modified

### Core Implementation
- `ProductsScreenViewModel.kt` - Table-based status management
- `CartScreenFigma.kt` - Updated function calls
- `CourseStatusTest.kt` - Comprehensive tests

### Key Changes
1. **State Structure**: Added table-based status maps
2. **Status Functions**: Updated to handle table context
3. **Table Management**: Added table selection/removal handlers
4. **Memory Management**: Automatic cleanup on table removal
5. **Testing**: Comprehensive test coverage

## Future Enhancements

### Potential Improvements
1. **Status Persistence**: Save table statuses to database
2. **Status Sync**: Sync statuses across devices
3. **Time Tracking**: Track how long courses stay in each status
4. **Kitchen Integration**: Allow kitchen to update statuses
5. **Status History**: Maintain history of status changes per table

The implementation provides a robust, scalable system for managing course statuses across multiple tables while maintaining clean separation and automatic cleanup.
