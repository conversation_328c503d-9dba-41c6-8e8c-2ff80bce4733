package com.thedasagroup.suminative.ui.products

import com.airbnb.mvrx.Async
import com.thedasagroup.suminative.data.model.response.options_details.OptionDetails
import com.thedasagroup.suminative.data.repo.OptionRepository
import kotlinx.coroutines.flow.StateFlow

class OptionDetailsUseCase(private val optionRepository: OptionRepository) {
    suspend operator fun invoke(itemId: Int): StateFlow<Async<OptionDetails>> {
        // Get option details from local database instead of API
        return optionRepository.getOptionDetailsFromDatabase(itemId)
    }
}