package com.thedasagroup.suminative.ui.stock

import com.airbnb.mvrx.Async
import com.airbnb.mvrx.Success
import com.thedasagroup.suminative.BuildConfig
import com.thedasagroup.suminative.data.model.request.category_sorting.CategorySortingRequest
import com.thedasagroup.suminative.data.model.request.stock.GetPagedStockItemsRequest
import com.thedasagroup.suminative.data.model.response.stock.StockItemsResponse
import com.thedasagroup.suminative.data.prefs.Prefs
import com.thedasagroup.suminative.data.repo.ProductRepository
import com.thedasagroup.suminative.data.repo.StockRepository
import com.thedasagroup.suminative.data.model.response.stock.StockItem
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.filter

open class StockUseCase(private val stockRepository: StockRepository, private val prefs: Prefs,
    private val productRepository: ProductRepository,
    private val categorySortingHelper: CategorySortingHelper
) {
    suspend operator fun invoke(): StateFlow<Async<StockItemsResponse>> {
        val storeId = prefs.store?.id ?: 0
        
        // Get products from database instead of API
        val databaseProducts = productRepository.getProductsByStore(storeId)
        
        when (databaseProducts.value) {
            is Success -> {
                val products = databaseProducts.value() ?: emptyList()
                
                // Convert database entities to StockItem format
                val stockItems = products.map { productEntity ->
                    StockItem(
                        id = productEntity.productId?.toInt(),
                        name = productEntity.name,
                        description = productEntity.description,
                        additionalInfo = productEntity.additionalInfo,
                        category = productEntity.category,
                        categoryId = productEntity.categoryId?.toInt(),
                        price = productEntity.price,
                        billAmount = productEntity.billAmount,
                        tax = productEntity.tax,
                        vat = productEntity.vat == 1L,
                        pic = productEntity.pic,
                        stock = productEntity.stock?.toInt(),
                        ingredients = productEntity.ingredients,
                        preparationTime = productEntity.preparationTime,
                        servingSize = productEntity.servingSize,
                        dailyCapacity = productEntity.dailyCapacity?.toInt(),
                        discountType = productEntity.discountType?.toInt(),
                        discountedAmount = productEntity.discountedAmount?.toString(),
                        brandId = productEntity.brandId?.toInt(),
                        businessId = productEntity.businessId?.toInt(),
                        storeId = productEntity.storeId?.toInt(),
                        unitId = productEntity.unitId?.toInt(),
                        createdBy = productEntity.createdBy?.toInt(),
                        createdOn = productEntity.createdOn,
                        modifiedBy = productEntity.modifiedBy?.toInt(),
                        modifiedOn = productEntity.modifiedOn
                    )
                }
                
                val filteredItems = if (BuildConfig.SHOW_PRINTBILL.isNotEmpty()) {
                    stockItems.filter {
                        it.brandId == prefs.loginResponse?.brandId
                    }
                } else {
                    stockItems
                }
                
                // Get category sorting from API (still needed for category order)
//                val categorySortingResult = stockRepository.getCategorySorting(
//                    CategorySortingRequest(storeId = storeId.toString())
//                )

                val storeId = prefs.store?.id ?: 0L
                val sortedCategories = categorySortingHelper.getCategoriesForSorting(storeId.toLong())
                val sortedItems = filteredItems.sortedWith { item1, item2 ->
                    val index1 = sortedCategories.indexOf(item1.category)
                    val index2 = sortedCategories.indexOf(item2.category)

                    when {
                        index1 == -1 && index2 == -1 -> 0 // Both categories not in the sort list
                        index1 == -1 -> 1 // First category not in sort list, put it after
                        index2 == -1 -> -1 // Second category not in sort list, put it after
                        else -> index1.compareTo(index2) // Both in sort list, compare positions
                    }
                }
                
                return MutableStateFlow(
                    Success(
                        StockItemsResponse(
                            items = sortedItems,
                            totalCount = sortedItems.size,
                            success = true
                        )
                    )
                )
            }

            else -> {
                // Return empty response if database query fails
                return MutableStateFlow(
                    Success(
                        StockItemsResponse(
                            items = emptyList(),
                            totalCount = 0,
                            success = false
                        )
                    )
                )
            }
        }
    }
}