package com.thedasagroup.suminative.ui.categories

import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Menu
import androidx.compose.material.icons.outlined.Add
import androidx.compose.material.icons.outlined.Close
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.ScrollableTabRow
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.airbnb.mvrx.Loading
import com.airbnb.mvrx.Uninitialized
import com.airbnb.mvrx.compose.collectAsState
import com.thedasagroup.suminative.data.model.response.rewards.RewardStoreItem
import com.thedasagroup.suminative.data.model.response.stock.StockItem
import com.thedasagroup.suminative.ui.common.CartBottomBar
import com.thedasagroup.suminative.ui.products.ProductsScreenState
import com.thedasagroup.suminative.ui.products.ProductsScreenViewModel
import com.thedasagroup.suminative.ui.reservations.AreaTableSelectionHelper
import com.thedasagroup.suminative.ui.rewards.RewardsViewModel
import com.thedasagroup.suminative.ui.theme.fontPoppins
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

@Composable
fun CategoriesScreen(
    viewModel: ProductsScreenViewModel,
    rewardsViewModel : RewardsViewModel,
    onBackClick: () -> Unit,
    onCategoryClick: (String, List<StockItem>) -> Unit,
    onOpenCart: () -> Unit = {},
    onRemoveCustomer : () -> Unit,
    onAvailToCartSuccess : (RewardStoreItem?) -> Unit,
    onRewardsClick : () -> Unit
) {
    val context = LocalContext.current
    val response by viewModel.collectAsState(ProductsScreenState::stockItemsResponse)
    val order by viewModel.collectAsState { it.getCurrentTableOrder() }
    val selectedTables by viewModel.collectAsState(ProductsScreenState::selectedTables)
    val selectedTableIndex by viewModel.collectAsState(ProductsScreenState::selectedTableIndex)
    val cartItemCount = order.carts?.size ?: 0
    val state by viewModel.collectAsState()

    // Activity result launcher for area/table selection
    val areaTableSelectionLauncher = rememberLauncherForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { result ->
        val selection = AreaTableSelectionHelper.parseResult(result.resultCode, result.data)
        if (selection != null) {
            viewModel.addSelectedTable(selection)
        }
    }

    LaunchedEffect(key1 = "loadCategories") {
        withContext(Dispatchers.IO) {
            viewModel.getStockItems()
        }
    }

    Box(modifier = Modifier.fillMaxSize()) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .background(Color(0xFFF5F5F5)) // Light background
                .statusBarsPadding()
        ) {
            // Header with DASA branding and refresh button
            DasaProductsHeader(
                onBackClick = onBackClick,
                showBack = false,
                onOpenCart = onOpenCart,
                cartItemCount = cartItemCount,
                onRewardsClick = onRewardsClick,
                viewModel = viewModel
            )

            // Selected Customer Banner
            val rewardsState by rewardsViewModel.collectAsState()
            if (rewardsState.selectedCustomer != null) {
                com.thedasagroup.suminative.ui.rewards.SelectedCustomerBanner(
                    customerName = rewardsState.selectedCustomer!!.name ?: "Unknown Customer",
                    onRemove = {
                        onRemoveCustomer()
                    }
                )
            }

            // Table Selection Bar
            TableSelectionTabs(
                selectedTables = selectedTables,
                selectedTableIndex = selectedTableIndex,
                onTableSelected = { index ->
                    viewModel.setSelectedTableIndex(index)
                },
                onAddTableClick = {
                    // Get currently selected table IDs to exclude them from selection
                    val excludedTableIds = selectedTables.map { it.tableId }
                    AreaTableSelectionHelper.launchAreaTableSelection(
                        context as androidx.activity.ComponentActivity,
                        areaTableSelectionLauncher,
                        excludedTableIds
                    )
                },
                onRemoveTable = { tableId ->
                    viewModel.removeSelectedTable(tableId)
                }
            )

            when {
                response is Loading || response is Uninitialized -> {
                    LoadingState()
                }
                else -> {
                    val categories = response()?.mapCategories ?: emptyMap()
                    CategoriesContent(
                        categories = categories,
                        onCategoryClick = onCategoryClick,
                        modifier = Modifier.weight(1f)
                    )
                }
            }
        }
        
        // Bottom Cart Bar
        if (cartItemCount > 0) {
            CartBottomBar(
                itemCount = cartItemCount,
                onCartClick = onOpenCart,
                modifier = Modifier.align(Alignment.BottomCenter)
            )
        }
    }
}



@Composable
private fun DasaHeader() {
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .background(Color.White)
            .padding(horizontal = 16.dp, vertical = 12.dp)
    ) {
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = "DASA",
                fontSize = 24.sp,
                fontWeight = FontWeight.Bold,
                color = Color(0xFF2E7D32),
                fontFamily = fontPoppins
            )
            
            IconButton(onClick = { }) {
                Icon(
                    imageVector = Icons.Default.Menu,
                    contentDescription = "Menu",
                    tint = Color(0xFF2E7D32)
                )
            }
        }
    }
}

@Composable
private fun LoadingState() {
    Box(
        modifier = Modifier.fillMaxSize(),
        contentAlignment = Alignment.Center
    ) {
        CircularProgressIndicator(color = Color(0xFF2E7D32))
    }
}

@Composable
private fun CategoriesContent(
    categories: Map<String?, List<StockItem>>,
    onCategoryClick: (String, List<StockItem>) -> Unit,
    modifier: Modifier = Modifier
) {
    val categoryList = categories.toList().filter { it.first != null && it.second.isNotEmpty() }
    
    LazyColumn(
        modifier = modifier
            .fillMaxSize()
            .background(Color(0xFFF5F5F5)),
        contentPadding = PaddingValues(start = 16.dp, end = 16.dp, top = 20.dp, bottom = 80.dp),
        verticalArrangement = Arrangement.spacedBy(12.dp, Alignment.CenterVertically)
    ) {
        itemsIndexed(categoryList) { index, (categoryName, items) ->
            val isEven = index % 2 == 0
            CategoryButton(
                categoryName = categoryName?.uppercase() ?: "UNKNOWN",
                isGreenButton = isEven, // Alternate between green and light
                onClick = { onCategoryClick(categoryName ?: "Unknown", items) }
            )
        }
    }
}

@Composable
private fun CategoryButton(
    categoryName: String,
    isGreenButton: Boolean,
    onClick: () -> Unit
) {
    val backgroundColor = if (isGreenButton) {
        Color(0xFF2E7D32) // Dark green like in DASA design
    } else {
        Color(0xFFF5F5F5) // Light grey/white
    }
    
    val textColor = if (isGreenButton) {
        Color.White
    } else {
        Color.Black
    }
    
    val borderColor = if (!isGreenButton) {
        Color(0xFF003823) // Light border for light buttons
    } else {
        Color.Transparent
    }

    Box(
        modifier = Modifier
            .fillMaxWidth()
            .height(68.dp)
            .clip(RoundedCornerShape(12.dp))
            .background(backgroundColor)
            .clickable { onClick() }
            .border(2.dp, borderColor, RoundedCornerShape(12.dp))
            .then(
                if (!isGreenButton) {
                    Modifier.padding(1.dp).background(
                        Color.White,
                        RoundedCornerShape(12.dp)
                    )
                } else {
                    Modifier
                }
            ),
        contentAlignment = Alignment.Center
    ) {
        Text(
            text = categoryName,
            color = textColor,
            fontSize = 18.sp,
            fontWeight = FontWeight.Bold,
            fontFamily = fontPoppins,
            textAlign = TextAlign.Center
        )
    }
}

@Composable
fun TableSelectionTabs(
    selectedTables: List<AreaTableSelectionHelper.AreaTableSelection>,
    selectedTableIndex: Int,
    onTableSelected: (Int) -> Unit,
    onAddTableClick: () -> Unit,
    onRemoveTable: (Int) -> Unit
) {
    if (selectedTables.isNotEmpty() || true) { // Always show to allow adding tables
        ScrollableTabRow(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp, vertical = 8.dp),
            selectedTabIndex = selectedTableIndex,
            containerColor = Color.Transparent,
            edgePadding = 0.dp,
            indicator = {
            }
        ) {
            // Table tabs
            selectedTables.forEachIndexed { index, table ->
                TableTab(
                    table = table,
                    isSelected = index == selectedTableIndex,
                    onClick = { onTableSelected(index) },
                    onRemove = { onRemoveTable(table.tableId) }
                )
            }

            // Add button
            AddTableButton(onClick = onAddTableClick)
        }
    }
}

@Composable
fun TableTab(
    table: AreaTableSelectionHelper.AreaTableSelection,
    isSelected: Boolean,
    onClick: () -> Unit,
    onRemove: () -> Unit
) {
    val backgroundColor = if (isSelected) Color(0xFF2E7D32) else Color.White
    val textColor = if (isSelected) Color.White else Color(0xFF2E7D32)
    val borderColor = Color(0xFF2E7D32)

    Card(
        modifier = Modifier
            .clickable { onClick() }
            .border(
                width = 2.dp,
                color = borderColor,
                shape = RoundedCornerShape(8.dp)
            ),
        shape = RoundedCornerShape(8.dp),
        colors = CardDefaults.cardColors(containerColor = backgroundColor),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Row(
            modifier = Modifier.padding(horizontal = 12.dp, vertical = 8.dp),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(4.dp)
        ) {
            Text(
                text = table.tableName,
                color = textColor,
                fontFamily = fontPoppins,
                fontWeight = FontWeight.Bold,
                fontSize = 14.sp
            )

            IconButton(
                onClick = onRemove,
                modifier = Modifier.size(20.dp)
            ) {
                Icon(
                    imageVector = Icons.Outlined.Close,
                    contentDescription = "Remove table",
                    tint = textColor,
                    modifier = Modifier.size(16.dp)
                )
            }
        }
    }
}

@Composable
fun AddTableButton(onClick: () -> Unit) {
    Card(
        modifier = Modifier
            .clickable { onClick() }
            .border(
                width = 2.dp,
                color = Color(0xFF2E7D32),
                shape = RoundedCornerShape(8.dp)
            ),
        shape = RoundedCornerShape(8.dp),
        colors = CardDefaults.cardColors(containerColor = Color.White),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Row(
            modifier = Modifier.padding(horizontal = 12.dp, vertical = 8.dp),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(4.dp)
        ) {
            Icon(
                imageVector = Icons.Outlined.Add,
                contentDescription = "Add table",
                tint = Color(0xFF2E7D32),
                modifier = Modifier.size(20.dp)
            )
            Text(
                text = "Add Table",
                color = Color(0xFF2E7D32),
                fontFamily = fontPoppins,
                fontWeight = FontWeight.Bold,
                fontSize = 14.sp
            )
        }
    }
}