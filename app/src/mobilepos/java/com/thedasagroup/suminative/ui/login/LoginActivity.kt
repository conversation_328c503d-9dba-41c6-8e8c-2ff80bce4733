package com.thedasagroup.suminative.ui.login

import android.content.Intent
import android.os.Build
import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.lifecycle.lifecycleScope
import com.afollestad.materialdialogs.MaterialDialog
import com.airbnb.mvrx.MavericksView
import com.airbnb.mvrx.viewModel
import com.thedasagroup.suminative.ui.categories.CategoriesActivity
import com.thedasagroup.suminative.ui.products.ProductsScreenViewModel
import com.thedasagroup.suminative.ui.service.Actions
import com.thedasagroup.suminative.ui.service.EndlessSocketService
import com.thedasagroup.suminative.ui.service.ServiceState
import com.thedasagroup.suminative.ui.service.getServiceState
import com.thedasagroup.suminative.ui.service.log
import com.thedasagroup.suminative.ui.stores.DownloadProductsActivity
import com.thedasagroup.suminative.ui.stores.SelectStoreActivity
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlin.also
import kotlin.collections.firstOrNull
import kotlin.jvm.java

class LoginActivity : ComponentActivity() , MavericksView{

    val loginScreenViewModel : LoginScreenViewModel by viewModel()
    val productsScreenViewModel : ProductsScreenViewModel by viewModel()

    /*private val pushNotificationPermissionLauncher = registerForActivityResult(
        ActivityResultContracts.RequestPermission()
    ) { granted ->
        if (granted) {
            log("Permission granted")
        } else {
            log("Permission denied")
        }
    }*/

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContent {
            LoginScreen(
                loginViewModel = loginScreenViewModel,
                onLoginSuccess = {
                    val stores = loginScreenViewModel.prefs.loginResponse?.stores ?: mutableListOf()
                    if(stores.size > 1) {
                        val intent = Intent(this, SelectStoreActivity::class.java)
                        intent.setFlags(Intent.FLAG_ACTIVITY_CLEAR_TASK or Intent.FLAG_ACTIVITY_NEW_TASK)
                        startActivity(intent)
                    }
                    else {
                        val store = stores.firstOrNull()
                        val storeId = store?.id ?: 0
                        loginScreenViewModel.prefs.store = store
                        lifecycleScope.launch(Dispatchers.IO){
                            val existingProducts = productsScreenViewModel.productsRepository.getProductsByStore(storeId)
                            if (existingProducts.value()?.isNotEmpty() != true) {
                                navigateToDownloadProducts()
                            } else {
                                // Navigate to main screen
                                navigateToMainScreen()
                            }
                        }
                    }
                },
                onLoginError = {error ->
                    MaterialDialog(this@LoginActivity).show {
                        message(text = error)
                        positiveButton(text = "Ok") {
                            it.dismiss()
                        }
                    }
                }
            )
        }
        /*if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            pushNotificationPermissionLauncher.launch(android.Manifest.permission.POST_NOTIFICATIONS)
        }*/
    }

    private fun navigateToDownloadProducts() {
        log("Navigating to Download Products Screen")
        val intent = Intent(this, DownloadProductsActivity::class.java)
        intent.setFlags(Intent.FLAG_ACTIVITY_CLEAR_TASK or Intent.FLAG_ACTIVITY_NEW_TASK)
        startActivity(intent)
    }
    private fun navigateToMainScreen() {
        log("Navigating to Main Screen")
        val intent = Intent(this, CategoriesActivity::class.java)
        intent.setFlags(Intent.FLAG_ACTIVITY_CLEAR_TASK or Intent.FLAG_ACTIVITY_NEW_TASK)
        startActivity(intent)
    }

    override fun invalidate() {

    }

    private fun scheduleJob(){
        actionOnService(Actions.START)
    }

    private fun actionOnService(action: Actions) {
        if (getServiceState(this) == ServiceState.STOPPED && action == Actions.STOP) return
        Intent(this, EndlessSocketService::class.java).also {
            it.action = action.name
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                log("Starting the service in >=26 Mode")
                startForegroundService(it)
                return
            }
            log("Starting the service in < 26 Mode")
            startService(it)
        }
    }
}