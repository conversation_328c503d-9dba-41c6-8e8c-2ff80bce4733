package com.thedasagroup.suminative.di

import com.thedasagroup.suminative.data.prefs.Prefs
import com.thedasagroup.suminative.data.repo.OptionRepository
import com.thedasagroup.suminative.data.repo.ProductRepository
import com.thedasagroup.suminative.data.repo.StockRepository
import com.thedasagroup.suminative.ui.products.OptionDetailsUseCase
import com.thedasagroup.suminative.ui.stock.CategorySortingHelper
import com.thedasagroup.suminative.ui.stock.ChangeStockUseCase
import com.thedasagroup.suminative.ui.stock.StockUseCase
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
internal object OrderUseCaseModule {
    @Singleton
    @Provides
    fun providesOptionDetailsUseCase(
        optionRepository: OptionRepository
    ): OptionDetailsUseCase {
        return OptionDetailsUseCase( optionRepository = optionRepository)
    }

    @Singleton
    @Provides
    fun providesStockUseCase(stockRepository: StockRepository, prefs: Prefs, productRepository: ProductRepository,
                             categorySortingHelper: CategorySortingHelper
    ): StockUseCase {
        return StockUseCase(stockRepository = stockRepository, prefs = prefs, productRepository = productRepository,
            categorySortingHelper = categorySortingHelper)
    }

    @Singleton
    @Provides
    fun providesUpdateStockUseCase(
        stockRepository: StockRepository,
        prefs: Prefs,
        productRepository: ProductRepository,
        categorySortingHelper: CategorySortingHelper
    ): ChangeStockUseCase {
        return ChangeStockUseCase(stockRepository = stockRepository, prefs = prefs, productRepository = productRepository,
            categorySortingHelper = categorySortingHelper)
    }
}