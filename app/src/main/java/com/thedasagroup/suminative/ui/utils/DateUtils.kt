package com.thedasagroup.suminative.ui.utils

import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Date
import java.util.GregorianCalendar
import java.util.Locale
import java.util.TimeZone

val DATE_FORMAT_BACK_END = "yyyy-MM-dd'T'HH:mm:ss'Z'"
val DATE_FORMAT_BACK_END2 = "yyyy-MM-dd'T'HH:mm:ss.S'Z'"
val DATE_FORMAT_APP = "yyyy-MM-dd HH:mm:ss"
val DATE_FORMAT_DATE_ONLY = "yyyy-MM-dd"
val DATE_FORMAT_RESERVATIONS = "yyyy-MM-dd'T'HH:mm:ss"
val DATE_FORMAT_GUAVA = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'"

fun String.formatDate(format : String) : String{
    val mCalendar: Calendar = GregorianCalendar()
    val mTimeZone: TimeZone = mCalendar.timeZone
    val inputFormat = SimpleDateFormat(format, Locale.getDefault())
    inputFormat.timeZone = TimeZone.getTimeZone("UTC");
    val outputFormat = SimpleDateFormat("dd/MM/yyyy hh:mm:ss a", Locale.getDefault())
    outputFormat.timeZone = mTimeZone;
    val date = inputFormat.parse(this) ?: Date()
    return outputFormat.format(date)
}

fun String.formatDatePrint(format : String) : String{
    val mCalendar: Calendar = GregorianCalendar()
    val mTimeZone: TimeZone = mCalendar.timeZone
    val inputFormat = SimpleDateFormat(format, Locale.getDefault())
    inputFormat.timeZone = TimeZone.getTimeZone("UTC");
    val outputFormat = SimpleDateFormat("dd/MM/yyyy, hh:mm a", Locale.getDefault())
    outputFormat.timeZone = mTimeZone;
    val date = inputFormat.parse(this) ?: Date()
    return outputFormat.format(date)
}

fun String.toDate(format: String) : Date{
    val inputFormat = SimpleDateFormat(format, Locale.getDefault())
    inputFormat.timeZone = TimeZone.getTimeZone("UTC");
    val date = inputFormat.parse(this) ?: Date()
    return date
}

fun Date.toGMT() : Date{
    return this
}

fun Double.transformDecimal() : String{
    return String.format("%.2f", this)
}

fun Date.formatDate(format: String) : String{
    val outputFormat = SimpleDateFormat(format, Locale.getDefault())
    return outputFormat.format(this)
}

/*fun String.toDate() : Date{
    val outputFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault())
    return outputFormat.parse(this.split(".")[0]) ?: Date()
}*/

fun getMinutesBetweenTwoDates(startDateString: String, endDateString: String, formatStart : String, formatEnd : String) : Long {
    val startDate = startDateString.toDate(formatStart)
    val endDate: Date = endDateString.toDate(formatEnd)
    return getMinutesBetweenTwoDates(startDate, endDate)
}

fun getMinutesBetweenTwoDates(startDate: Date, endDate: Date) : Long {
    var different = endDate.time - startDate.time
    println("startDate : $startDate")
    println("endDate : $endDate")
    println("different : $different")

    val secondsInMilli: Long = 1000
    val minutesInMilli = secondsInMilli * 60
    val hoursInMilli = minutesInMilli * 60
    val daysInMilli = hoursInMilli * 24

    val elapsedDays = different / daysInMilli
    different = different % daysInMilli

    val elapsedHours = different / hoursInMilli
    different = different % hoursInMilli

    val elapsedMinutes = different / minutesInMilli
    different = different % minutesInMilli

    val elapsedSeconds = different / secondsInMilli
    return elapsedMinutes
}


fun getDateFromHourAndMinute(hour: Int, minute: Int, nowDate : Date): Date {
    val calendar = Calendar.getInstance()
    calendar.time = nowDate
    calendar.set(Calendar.HOUR_OF_DAY, hour)
    calendar.set(Calendar.MINUTE, minute)
    calendar.set(Calendar.SECOND, 0)
    calendar.set(Calendar.MILLISECOND, 0)
    return calendar.time
}

fun Date.getDayOfWeek() : String{
    val sdf = SimpleDateFormat("EEEE")
    val d = this
    return sdf.format(d)
}

fun formatDateToHourAndMinute(date: Date): String {
    val calendar = Calendar.getInstance()
    calendar.time = date
    val inputFormat = SimpleDateFormat("hh:mm a", Locale.getDefault())
    return inputFormat.format(date)
}