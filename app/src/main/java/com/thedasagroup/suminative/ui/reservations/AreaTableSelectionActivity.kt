package com.thedasagroup.suminative.ui.reservations

import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.airbnb.mvrx.MavericksView
import com.airbnb.mvrx.compose.collectAsState
import com.airbnb.mvrx.compose.mavericksActivityViewModel
import com.airbnb.mvrx.viewModel
import com.thedasagroup.suminative.data.model.response.reservations.Area
import com.thedasagroup.suminative.data.model.response.reservations.Table
import com.thedasagroup.suminative.ui.theme.SumiNativeTheme
import com.thedasagroup.suminative.ui.utils.transformDecimal
import dagger.hilt.android.AndroidEntryPoint
import kotlin.math.max

@AndroidEntryPoint
class AreaTableSelectionActivity : AppCompatActivity(), MavericksView {
    val viewModel: ReservationsViewModel by viewModel()
    val productsViewModel: com.thedasagroup.suminative.ui.products.ProductsScreenViewModel by viewModel()

    companion object {
        const val EXTRA_SELECTED_AREA_ID = "selected_area_id"
        const val EXTRA_SELECTED_AREA_NAME = "selected_area_name"
        const val EXTRA_SELECTED_TABLE_ID = "selected_table_id"
        const val EXTRA_SELECTED_TABLE_NAME = "selected_table_name"
        const val EXTRA_SELECTED_TABLE_CAPACITY = "selected_table_capacity"
        const val EXTRA_EXCLUDED_TABLE_IDS = "excluded_table_ids"

        fun createIntent(context: Context, excludedTableIds: List<Int> = emptyList()): Intent {
            return Intent(context, AreaTableSelectionActivity::class.java).apply {
                putIntegerArrayListExtra(EXTRA_EXCLUDED_TABLE_IDS, ArrayList(excludedTableIds))
            }
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // Get excluded table IDs from intent
        val excludedTableIds = intent.getIntegerArrayListExtra(EXTRA_EXCLUDED_TABLE_IDS) ?: arrayListOf()

        setContent {
            SumiNativeTheme {
                AreaTableSelectionScreen(
                    excludedTableIds = excludedTableIds,
                    onAreaTableSelected = { area, table ->
                        val resultIntent = Intent().apply {
                            putExtra(EXTRA_SELECTED_AREA_ID, area.id)
                            putExtra(EXTRA_SELECTED_AREA_NAME, area.description)
                            putExtra(EXTRA_SELECTED_TABLE_ID, table.id)
                            putExtra(EXTRA_SELECTED_TABLE_NAME, table.tableName)
                            putExtra(EXTRA_SELECTED_TABLE_CAPACITY, table.seatingCapacity)
                        }
                        setResult(RESULT_OK, resultIntent)
                        finish()
                    },
                    onBackPressed = {
                        setResult(RESULT_CANCELED)
                        finish()
                    },
                    viewModel = viewModel,
                    productsViewModel = productsViewModel
                )
            }
        }
    }

    override fun invalidate() {

    }
}

@Composable
fun AreaTableSelectionScreen(
    excludedTableIds: List<Int> = emptyList(),
    onAreaTableSelected: (Area, Table) -> Unit,
    onBackPressed: () -> Unit,
    viewModel: ReservationsViewModel,
    productsViewModel: com.thedasagroup.suminative.ui.products.ProductsScreenViewModel
) {
    val state by viewModel.collectAsState()
    val productsState by productsViewModel.collectAsState()

    LaunchedEffect(key1 = "getReservationsAreas") {
        viewModel.loadReservationAreas()
    }

    // Load tables for all areas when areas are loaded
    LaunchedEffect(state.areasResponse) {
        if (state.areasResponse is com.airbnb.mvrx.Success) {
            viewModel.loadTablesForAllAreas()
        }
    }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(Color.White)
            .padding(24.dp)
    ) {
        // Header
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Button(
                onClick = onBackPressed,
                colors = ButtonDefaults.buttonColors(
                    containerColor = Color.Gray.copy(alpha = 0.2f),
                    contentColor = Color.Black
                )
            ) {
                Text("Back")
            }

            Text(
                text = "Select a table:",
                fontSize = 24.sp,
                fontWeight = FontWeight.Medium,
                color = Color.Black
            )

            Spacer(modifier = Modifier.width(80.dp)) // Balance the back button
        }

        Spacer(modifier = Modifier.height(32.dp))

        // Area tabs and table content
        AreaTabsWithTablesContent(
            state = state,
            excludedTableIds = excludedTableIds,
            onTableSelected = { area, table ->
                onAreaTableSelected(area, table)
            },
            onAreaTabSelected = { index ->
                viewModel.setSelectedAreaTabIndex(index)
            },
            tableOrders = productsState.tableOrders
        )
    }
}

@Composable
fun AreaTabsWithTablesContent(
    state: ReservationsState,
    excludedTableIds: List<Int> = emptyList(),
    onTableSelected: (Area, Table) -> Unit,
    onAreaTabSelected: (Int) -> Unit,
    tableOrders: Map<Int, com.thedasagroup.suminative.data.model.request.order.Order> = emptyMap()
) {
    when (val areasResponse = state.areasResponse) {
        is com.airbnb.mvrx.Loading -> {
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                CircularProgressIndicator(
                    color = Color(0xFF2E7D32) // Green color matching POS theme
                )
            }
        }
        is com.airbnb.mvrx.Success -> {
            val areas = areasResponse.invoke()

            if (areas.isEmpty()) {
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        text = "No dining sections available",
                        fontSize = 18.sp,
                        color = Color.Gray
                    )
                }
            } else {
                Column(modifier = Modifier.fillMaxSize()) {
                    // Area Tabs
                    AreaTabRow(
                        areas = areas,
                        selectedTabIndex = state.selectedAreaTabIndex,
                        onTabSelected = onAreaTabSelected
                    )

                    Spacer(modifier = Modifier.height(16.dp))

                    // Tables for selected area
                    if (state.selectedAreaTabIndex < areas.size) {
                        val selectedArea = areas[state.selectedAreaTabIndex]
                        AreaTablesContent(
                            area = selectedArea,
                            tablesResponse = state.areaTablesMap[selectedArea.id] ?: com.airbnb.mvrx.Uninitialized,
                            excludedTableIds = excludedTableIds,
                            onTableSelected = { table ->
                                onTableSelected(selectedArea, table)
                            },
                            tableOrders = tableOrders
                        )
                    }
                }
            }
        }
        is com.airbnb.mvrx.Fail -> {
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Text(
                        text = "Failed to load dining sections",
                        fontSize = 18.sp,
                        color = Color.Red
                    )
                    Spacer(modifier = Modifier.height(8.dp))
                    Text(
                        text = "Please try again",
                        fontSize = 14.sp,
                        color = Color.Gray
                    )
                }
            }
        }
        else -> {
            // Uninitialized state
        }
    }
}

@Composable
fun AreaTabRow(
    areas: List<Area>,
    selectedTabIndex: Int,
    onTabSelected: (Int) -> Unit
) {
    ScrollableTabRow(
        selectedTabIndex = selectedTabIndex,
        containerColor = Color.Transparent,
        modifier = Modifier.fillMaxWidth(),
        indicator = { tabPositions ->
            // No indicator needed since we're using custom tab backgrounds
        },
        edgePadding = 0.dp
    ) {
        areas.forEachIndexed { index, area ->
            AreaTab(
                area = area,
                isSelected = index == selectedTabIndex,
                onClick = { onTabSelected(index) }
            )
        }
    }
}

@Composable
fun AreaTab(
    area: Area,
    isSelected: Boolean,
    onClick: () -> Unit
) {
    val backgroundColor = if (isSelected) Color(0xFF2E7D32) else Color.Transparent
    val textColor = if (isSelected) Color.White else Color.Black
    val borderColor = if (isSelected) Color.Transparent else Color(0xFF2E7D32)

    Card(
        modifier = Modifier
            .padding(horizontal = 4.dp, vertical = 8.dp)
            .clickable { onClick() },
        shape = RoundedCornerShape(12.dp),
        colors = CardDefaults.cardColors(
            containerColor = backgroundColor
        ),
        border = BorderStroke(2.dp, borderColor),
        elevation = CardDefaults.cardElevation(defaultElevation = if (isSelected) 4.dp else 2.dp)
    ) {
        Box(
            modifier = Modifier.padding(horizontal = 16.dp, vertical = 12.dp),
            contentAlignment = Alignment.Center
        ) {
            Text(
                text = area.description,
                fontSize = 16.sp,
                fontWeight = if (isSelected) FontWeight.Bold else FontWeight.Medium,
                color = textColor,
                textAlign = TextAlign.Center
            )
        }
    }
}

@Composable
fun AreaTablesContent(
    area: Area,
    tablesResponse: com.airbnb.mvrx.Async<List<Table>>,
    excludedTableIds: List<Int> = emptyList(),
    onTableSelected: (Table) -> Unit,
    tableOrders: Map<Int, com.thedasagroup.suminative.data.model.request.order.Order> = emptyMap()
) {
    when (tablesResponse) {
        is com.airbnb.mvrx.Loading -> {
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                CircularProgressIndicator(
                    color = Color(0xFF2E7D32)
                )
            }
        }
        is com.airbnb.mvrx.Success -> {
            val allTables = tablesResponse.invoke()
            // Filter out already selected tables
            val availableTables = allTables.filter { table ->
                table.id !in excludedTableIds
            }

            if (availableTables.isEmpty()) {
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center
                ) {
                    Column(
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        Text(
                            text = if (allTables.isEmpty()) {
                                "No tables available in ${area.description}"
                            } else {
                                "All tables in ${area.description} are already selected"
                            },
                            fontSize = 18.sp,
                            color = Color.Gray,
                            textAlign = TextAlign.Center
                        )
                        if (allTables.isNotEmpty() && availableTables.isEmpty()) {
                            Spacer(modifier = Modifier.height(8.dp))
                            Text(
                                text = "Try selecting a different section",
                                fontSize = 14.sp,
                                color = Color.Gray,
                                textAlign = TextAlign.Center
                            )
                        }
                    }
                }
            } else {
                // Use box layout with positioning based on tableDetailsJson
                TableBoxLayout(
                    tables = availableTables,
                    onTableSelected = onTableSelected,
                    tableOrders = tableOrders
                )
            }
        }
        is com.airbnb.mvrx.Fail -> {
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Text(
                        text = "Failed to load tables for ${area.description}",
                        fontSize = 18.sp,
                        color = Color.Red
                    )
                    Spacer(modifier = Modifier.height(8.dp))
                    Text(
                        text = "Please try again",
                        fontSize = 14.sp,
                        color = Color.Gray
                    )
                }
            }
        }
        else -> {
            // Uninitialized state - show loading
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                CircularProgressIndicator(
                    color = Color(0xFF2E7D32)
                )
            }
        }
    }
}



@Composable
fun TableCard(
    table: Table,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    cartTotal: Double = 0.0
) {
    val isOccupied = table.occupied
    val isReserved = table.reserved
    val tableDetails = table.getTableDetails()

    // Parse color from tableDetailsJson or use default
    val customColor = try {
        tableDetails?.color?.let { colorString ->
            if (colorString.startsWith("#")) {
                Color(android.graphics.Color.parseColor(colorString))
            } else {
                Color(0xFF2E7D32) // Default green
            }
        } ?: Color(0xFF2E7D32)
    } catch (e: Exception) {
        Color(0xFF2E7D32) // Default green if parsing fails
    }

    val backgroundColor = when {
        isOccupied -> Color.Red.copy(alpha = 0.7f)
        isReserved -> Color.Yellow.copy(alpha = 0.7f)
        else -> customColor.copy(alpha = 0.1f) // Light version of custom color
    }
    val textColor = when {
        isOccupied || isReserved -> Color.White
        else -> Color.Black
    }
    val borderColor = customColor

    // Determine shape from tableDetailsJson
    val shape = when (tableDetails?.shape?.uppercase()) {
        "CIRCLE" -> CircleShape
        "ROUND" -> CircleShape
        "SQUARE" -> RoundedCornerShape(8.dp)
        "RECTANGLE" -> RoundedCornerShape(12.dp)
        else -> RoundedCornerShape(12.dp) // Default to rectangle
    }

    Card(
        modifier = modifier
            .clickable(enabled = !isOccupied && !isReserved) { onClick() }
            .border(
                width = 2.dp,
                color = borderColor,
                shape = shape
            ),
        shape = shape,
        colors = CardDefaults.cardColors(
            containerColor = backgroundColor
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Box(
            modifier = Modifier.fillMaxSize(),
            contentAlignment = Alignment.Center
        ) {
            Column(
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.Center
            ) {
                Text(
                    text = table.tableName,
                    fontSize = 14.sp,
                    fontWeight = FontWeight.Bold,
                    color = textColor,
                    textAlign = TextAlign.Center,
                    maxLines = 1
                )
                Spacer(modifier = Modifier.height(2.dp))
                Text(
                    text = "Seats ${table.seatingCapacity}",
                    fontSize = 12.sp,
                    color = textColor.copy(alpha = 0.8f),
                    textAlign = TextAlign.Center,
                    maxLines = 1
                )

                // Cart total display
                if (cartTotal > 0.0) {
                    Spacer(modifier = Modifier.height(4.dp))
                    Text(
                        text = "£${cartTotal.transformDecimal()}",
                        fontSize = 12.sp,
                        color = Color(0xFF2E7D32), // Green color matching POS theme
                        fontWeight = FontWeight.Bold,
                        textAlign = TextAlign.Center
                    )
                }

                // Status indicator
                if (isOccupied) {
                    Spacer(modifier = Modifier.height(4.dp))
                    Text(
                        text = "Occupied",
                        fontSize = 12.sp,
                        color = Color.White,
                        fontWeight = FontWeight.Bold
                    )
                } else if (isReserved) {
                    Spacer(modifier = Modifier.height(4.dp))
                    Text(
                        text = "Reserved",
                        fontSize = 12.sp,
                        color = Color.White,
                        fontWeight = FontWeight.Bold
                    )
                }
            }
        }
    }
}

@Composable
fun TableBoxLayout(
    tables: List<Table>,
    onTableSelected: (Table) -> Unit,
    tableOrders: Map<Int, com.thedasagroup.suminative.data.model.request.order.Order> = emptyMap()
) {
    // Separate tables with and without position data
    val tablesWithPosition = tables.filter { it.getTableDetails()?.position != null }
    val tablesWithoutPosition = tables.filter { it.getTableDetails()?.position == null }

//    if (tablesWithPosition.isEmpty()) {
//        // Fallback to scrollable grid layout if no tables have position data
//        LazyVerticalGrid(
//            columns = GridCells.Fixed(3),
//            horizontalArrangement = Arrangement.spacedBy(16.dp),
//            verticalArrangement = Arrangement.spacedBy(16.dp),
//            modifier = Modifier
//                .fillMaxWidth()
//                .fillMaxHeight()
//        ) {
//            items(tables) { table ->
//                TableCard(
//                    table = table,
//                    onClick = { onTableSelected(table) },
//                    modifier = Modifier.height(80.dp)
//                )
//            }
//        }
//        return
//    }

    // Use fixed 8x8 grid dimensions (can be overridden by tableDetailsJson)
    val firstTableDetails = tablesWithPosition.firstOrNull()?.getTableDetails()
    val gridRows = firstTableDetails?.totalRows ?: 8
    val gridCols = firstTableDetails?.totalColumns ?: 8

    // Create a map of position to table for quick lookup
    val tablePositionMap = tablesWithPosition.associateBy { table ->
        val details = table.getTableDetails()
        Pair(details?.position?.row ?: 1, details?.position?.col ?: 1)
    }

    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp)
            .verticalScroll(rememberScrollState())
    ) {
        // Create all rows from 1 to gridRows to show proper spacing
        for (row in 1..gridRows) {
            Row(
                horizontalArrangement = Arrangement.spacedBy(8.dp),
                modifier = Modifier
                    .fillMaxWidth()
                    .height(80.dp) // Fixed height for consistent spacing
            ) {
                // Create columns
                for (col in 1..gridCols) {
                    val table = tablePositionMap[Pair(row, col)]
                    if (table != null) {
                        val cartTotal = tableOrders[table.id]?.totalPrice ?: 0.0
                        TableCard(
                            table = table,
                            onClick = { onTableSelected(table) },
                            modifier = Modifier
                                .weight(1f)
                                .fillMaxHeight(),
                            cartTotal = cartTotal
                        )
                    } else {
                        // Empty space to maintain grid structure - no visible grid lines
                        // This ensures proper spacing even for empty rows
                        Spacer(
                            modifier = Modifier
                                .weight(1f)
                                .fillMaxHeight()
                        )
                    }
                }
            }

            // Add vertical spacing between rows
            if (row < gridRows) {
                Spacer(modifier = Modifier.height(8.dp))
            }
        }

        // Add tables without position data at the bottom
//        if (tablesWithoutPosition.isNotEmpty()) {
//            Spacer(modifier = Modifier.height(24.dp))
//            Text(
//                text = "Additional Tables",
//                fontSize = 18.sp,
//                fontWeight = FontWeight.Medium,
//                color = Color.Black,
//                modifier = Modifier.padding(bottom = 16.dp)
//            )
//
//            // Create rows for additional tables in grid format
//            val additionalTableChunks = tablesWithoutPosition.chunked(3)
//            additionalTableChunks.forEach { rowTables ->
//                Row(
//                    horizontalArrangement = Arrangement.spacedBy(8.dp),
//                    modifier = Modifier
//                        .fillMaxWidth()
//                        .height(80.dp)
//                ) {
//                    rowTables.forEach { table ->
//                        TableCard(
//                            table = table,
//                            onClick = { onTableSelected(table) },
//                            modifier = Modifier
//                                .weight(1f)
//                                .fillMaxHeight()
//                        )
//                    }
//                    // Fill remaining spaces if row is not complete
//                    repeat(3 - rowTables.size) {
//                        Spacer(modifier = Modifier.weight(1f))
//                    }
//                }
//                Spacer(modifier = Modifier.height(8.dp))
//            }
//        }
    }
}
