package com.thedasagroup.suminative.ui.rewards

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.Close
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import com.airbnb.mvrx.Success
import com.airbnb.mvrx.compose.collectAsState
import com.thedasagroup.suminative.data.model.response.rewards.RewardsCustomer
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.ui.text.input.ImeAction
import kotlinx.coroutines.launch

// Theme colors
private val ThemeGreen = Color(0xFF2E7D32)
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun RewardsScreen(
    viewModel: RewardsViewModel,
    onBackClick: () -> Unit,
    onAddPointsClick: () -> Unit,
    onAddToCart: (rewardItem: com.thedasagroup.suminative.data.model.response.rewards.RewardItem)-> Unit,
    modifier: Modifier = Modifier,
    showHeader: Boolean = true,
    onStartQRScan: () -> Unit
) {
    val state by viewModel.collectAsState()
    val coroutineScope = rememberCoroutineScope()

    // Get business ID from preferences for placeholder
    val businessIdPlaceHolder = viewModel.prefs.loginResponse?.businesses?.id ?: 12
    val placeholderText = "dasa-$businessIdPlaceHolder-987"
//
//    // Auto-start QR scan when no customer is selected and QR scan callback is available
//    LaunchedEffect(state.selectedCustomer, state.customerIdInput) {
//        if (state.selectedCustomer == null &&
//            state.customerIdInput.isEmpty() &&
//            !state.isLoading) {
//            onStartQRScan()
//        }
//    }
    
    Column(
        modifier = modifier
            .fillMaxSize()
            .background(Color.White)
            .padding(16.dp)
    ) {
        // Top App Bar (conditionally shown)
        if (showHeader) {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(bottom = 24.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                IconButton(onClick = onBackClick) {
                    Icon(
                        imageVector = Icons.Default.ArrowBack,
                        contentDescription = "Back",
                        tint = Color.Black
                    )
                }
                Text(
                    text = "Customer Rewards",
                    fontSize = 24.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color.Black,
                    modifier = Modifier.padding(start = 8.dp)
                )
            }
        }

        Column(
            modifier = Modifier
                .fillMaxSize()
                .verticalScroll(rememberScrollState())
        ) {
            // Customer ID Input Section
            Card(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(bottom = 16.dp),
                shape = RoundedCornerShape(12.dp),
                colors = CardDefaults.cardColors(containerColor = Color(0xFFF5F5F5))
            ) {
                Column(
                    modifier = Modifier.padding(16.dp)
                ) {
                    Text(
                        text = "Search Customer",
                        fontSize = 18.sp,
                        fontWeight = FontWeight.SemiBold,
                        color = Color.Black,
                        modifier = Modifier.padding(bottom = 12.dp)
                    )
                    
                    OutlinedTextField(
                        value = state.customerIdInput,
                        onValueChange = { viewModel.updateCustomerIdInput(it) },
                        label = { Text("Customer ID") },
                        placeholder = { Text(placeholderText) },
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(bottom = 16.dp),
                        keyboardOptions = KeyboardOptions(
                            keyboardType = KeyboardType.Text,
                            imeAction = ImeAction.Search
                        ),
                        singleLine = true
                    )
                    
                    Button(
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(50.dp),
                        onClick = {
                            if (state.customerIdInput.isNotEmpty() && !state.isLoading) {
                                // Extract customer ID from input format (dasa-businessId-customerId)
                                val customerId = extractCustomerId(state.customerIdInput)
                                val businessIdInput = extractBusinessId(state.customerIdInput)
                                if (customerId != null) {
                                    coroutineScope.launch {
                                        viewModel.getAllCustomers(customerId = customerId, businessId = businessIdInput ?: 0)
                                    }
                                }
                            }
                        },
                        colors = ButtonDefaults.buttonColors(containerColor = ThemeGreen),
                        shape = RoundedCornerShape(12.dp),
                        enabled = !state.isLoading
                    ) {
                        Text(
                            text = if (state.isLoading) "Searching..." else "Search Customer",
                            color = Color.White,
                            fontWeight = FontWeight.Medium
                        )
                    }
                }
            }
            
            // Customer Info Section
            state.getAllCustomersResponse.invoke()?.let { response ->
                if (response.success == true && response.data != null) {
                    val customer = response.data
                    viewModel.setSelectedCustomer(customer)

                    // Update selected customer in ViewModel state immediately
                    LaunchedEffect(customer) {
                        // Also fetch user points when customer is found
                        customer.id?.let { userId ->
                            viewModel.getUserPoints(userId, customer.businessId ?: 0)
                        }
                    }

                    CustomerInfoCard(
                        customer = customer,
                        userPoints = state.userPointsResponse.invoke(),
                        isLoadingPoints = state.userPointsResponse is com.airbnb.mvrx.Loading,
                        onAddPointsClick = {
                            onAddPointsClick()
                        },
                        onAvailRewardsClick = {
                            println("RewardsScreen: Avail Rewards clicked for customer: ${customer.name} (ID: ${customer.id})")
                            // Ensure customer is set and clear any previous rewards overview data
                            viewModel.setSelectedCustomer(customer)
                            viewModel.clearRewardsOverviewResponse()
                            viewModel.setShowRewardsAvailScreen(true)
                            println("RewardsScreen: showRewardsAvailScreen set to true")
                        }
                    )
                } else {
                    // Show error or no customer found
                    Card(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(bottom = 16.dp),
                        shape = RoundedCornerShape(12.dp),
                        colors = CardDefaults.cardColors(containerColor = Color(0xFFFFEBEE))
                    ) {
                        Text(
                            text = "No customer found or invalid customer ID",
                            modifier = Modifier.padding(16.dp),
                            color = Color(0xFFD32F2F)
                        )
                    }
                }
            }
            
            // Loading indicator
            if (state.isLoading) {
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(16.dp),
                    contentAlignment = Alignment.Center
                ) {
                    CircularProgressIndicator()
                }
            }
        }
    }

    // Rewards Avail Screen
    if (state.showRewardsAvailScreen && state.selectedCustomer != null) {
        println("RewardsScreen: Showing RewardsAvailScreen for customer: ${state.selectedCustomer?.name}")
        RewardsAvailScreen(
            viewModel = viewModel,
            customer = state.selectedCustomer!!,
            onBackClick = {
                println("RewardsScreen: RewardsAvailScreen back clicked")
                viewModel.setShowRewardsAvailScreen(false)
                viewModel.clearRewardsOverviewResponse()
            },
            onAddToCart = onAddToCart
        )
    } else {
        println("RewardsScreen: RewardsAvailScreen not shown - showRewardsAvailScreen: ${state.showRewardsAvailScreen}, selectedCustomer: ${state.selectedCustomer?.name}")
    }
}

@Composable
fun CustomerInfoCard(
    customer: RewardsCustomer,
    userPoints: Double?,
    isLoadingPoints: Boolean,
    onAddPointsClick: () -> Unit,
    onAvailRewardsClick: () -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(bottom = 16.dp),
        shape = RoundedCornerShape(12.dp),
        colors = CardDefaults.cardColors(containerColor = Color(0xFFE8F5E8))
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "Customer Information",
                fontSize = 18.sp,
                fontWeight = FontWeight.SemiBold,
                color = Color.Black,
                modifier = Modifier.padding(bottom = 12.dp)
            )
            
            CustomerInfoRow("Name", customer.name ?: "N/A")
            CustomerInfoRow("Email", customer.email ?: "N/A")
            CustomerInfoRow("Phone", customer.phone ?: "N/A")
            CustomerInfoRow("Customer ID", customer.id?.toString() ?: "N/A")
            CustomerInfoRow("Business ID", customer.businessId?.toString() ?: "N/A")

            // Current Points Section
            Spacer(modifier = Modifier.height(12.dp))
            Card(
                modifier = Modifier.fillMaxWidth(),
                shape = RoundedCornerShape(8.dp),
                colors = CardDefaults.cardColors(containerColor = ThemeGreen.copy(alpha = 0.1f))
            ) {
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(12.dp),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "Current Points:",
                        fontWeight = FontWeight.SemiBold,
                        color = ThemeGreen,
                        fontSize = 16.sp
                    )
                    if (isLoadingPoints) {
                        CircularProgressIndicator(
                            modifier = Modifier.size(20.dp),
                            color = ThemeGreen,
                            strokeWidth = 2.dp
                        )
                    } else {
                        Text(
                            text = "${userPoints?.toInt() ?: 0} pts",
                            fontWeight = FontWeight.Bold,
                            color = ThemeGreen,
                            fontSize = 18.sp
                        )
                    }
                }
            }

            Spacer(modifier = Modifier.height(16.dp))
            
            // Buttons Row
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(12.dp)
            ) {
                Button(
                    modifier = Modifier
                        .weight(1f)
                        .height(50.dp),
                    onClick = onAddPointsClick,
                    colors = ButtonDefaults.buttonColors(containerColor = ThemeGreen),
                    shape = RoundedCornerShape(12.dp)
                ) {
                    Text(
                        text = "Add Points",
                        color = Color.White,
                        fontWeight = FontWeight.Medium,
                        fontSize = 14.sp
                    )
                }

                Button(
                    modifier = Modifier
                        .weight(1f)
                        .height(50.dp),
                    onClick = onAvailRewardsClick,
                    colors = ButtonDefaults.buttonColors(
                        containerColor = if ((userPoints ?: 0.0) > 0) Color(0xFF1976D2) else Color(0xFF9E9E9E)
                    ),
                    shape = RoundedCornerShape(12.dp),
                    enabled = true // Always enabled, let the RewardsAvailScreen handle empty rewards
                ) {
                    Text(
                        text = if ((userPoints ?: 0.0) > 0) "Avail Rewards" else "View Rewards",
                        color = Color.White,
                        fontWeight = FontWeight.Medium,
                        fontSize = 14.sp
                    )
                }
            }
        }
    }
}

@Composable
fun CustomerInfoRow(label: String, value: String) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 4.dp),
        horizontalArrangement = Arrangement.SpaceBetween
    ) {
        Text(
            text = "$label:",
            fontWeight = FontWeight.Medium,
            color = Color.Black,
            modifier = Modifier.weight(1f)
        )
        Text(
            text = value,
            color = Color.Black,
            modifier = Modifier.weight(2f)
        )
    }
}

/**
 * Dialog version of the RewardsScreen for use in ProductsScreen
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun RewardsDialog(
    viewModel: RewardsViewModel,
    onDismiss: () -> Unit,
    onAddPointsClick: () -> Unit,
    onAddToCart: (rewardItem: com.thedasagroup.suminative.data.model.response.rewards.RewardItem) -> Unit,
    modifier: Modifier = Modifier,
    onStartQRScan: () -> Unit
) {
    Dialog(
        onDismissRequest = onDismiss,
        properties = DialogProperties(
            dismissOnBackPress = true,
            dismissOnClickOutside = true,
            usePlatformDefaultWidth = false
        )
    ) {
        Card(
            modifier = modifier,
            shape = RoundedCornerShape(16.dp),
            colors = CardDefaults.cardColors(containerColor = Color.White)
        ) {
            Column(
                modifier = Modifier.fillMaxSize()
            ) {
                // Dialog Header
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(16.dp),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "Customer Rewards",
                        fontSize = 24.sp,
                        fontWeight = FontWeight.Bold,
                        color = Color.Black
                    )
                    IconButton(onClick = onDismiss) {
                        Icon(
                            imageVector = Icons.Default.Close,
                            contentDescription = "Close",
                            tint = Color.Black
                        )
                    }
                }

                // Rewards Screen Content
                RewardsScreen(
                    viewModel = viewModel,
                    onBackClick = onDismiss,
                    onAddToCart = onAddToCart,
                    showHeader = false,
                    onStartQRScan = onStartQRScan,
                    onAddPointsClick = onAddPointsClick,
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(horizontal = 16.dp, vertical = 0.dp)
                )
            }
        }
    }
}

@Composable
fun SelectedCustomerBanner(
    customerName: String,
    onRemove: () -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp, vertical = 8.dp),
        shape = RoundedCornerShape(12.dp),
        colors = CardDefaults.cardColors(containerColor = ThemeGreen.copy(alpha = 0.1f)),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier.weight(1f)
            ) {
                Text(
                    text = "Customer Selected: ",
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Medium,
                    color = ThemeGreen
                )
                Text(
                    text = customerName,
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Bold,
                    color = ThemeGreen
                )
            }

            IconButton(
                onClick = onRemove,
                modifier = Modifier
                    .size(32.dp)
                    .background(
                        color = ThemeGreen.copy(alpha = 0.1f),
                        shape = CircleShape
                    )
            ) {
                Icon(
                    imageVector = Icons.Default.Close,
                    contentDescription = "Remove selected customer",
                    tint = ThemeGreen,
                    modifier = Modifier.size(20.dp)
                )
            }
        }
    }
}

fun extractCustomerId(input: String): Int? {
    return try {
        // Expected format: dasa-businessId-customerId
        val parts = input.split("-")
        if (parts.size == 3 && parts[0] == "dasa") {
            parts[2].toIntOrNull()
        } else {
            // If not in expected format, try to parse as direct number
            input.toIntOrNull()
        }
    } catch (e: Exception) {
        null
    }
}

fun extractBusinessId(input: String): Int? {
    return try {
        // Expected format: dasa-businessId-customerId
        val parts = input.split("-")
        if (parts.size == 3 && parts[0] == "dasa") {
            parts[1].toIntOrNull()
        } else {
            // If not in expected format, try to parse as direct number
            input.toIntOrNull()
        }
    } catch (e: Exception) {
        null
    }
}
