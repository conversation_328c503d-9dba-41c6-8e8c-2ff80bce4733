package com.thedasagroup.suminative.data.model.request.order

import android.os.Parcelable
import com.thedasagroup.suminative.data.model.response.login.Extra
import kotlinx.parcelize.Parcelize
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import java.util.UUID

@Serializable
@Parcelize
data class StoreItem(
    @SerialName("additionalInfo") val additionalInfo: String? = null,
    @SerialName("billAmount") val billAmount: Double? = 0.0,
    @SerialName("brandId") val brandId: Int? = 0,
    @SerialName("businessId") val businessId: Int? = 0,
    @SerialName("categoryId") val categoryId: Int? = 0,
    @SerialName("createdBy") val createdBy: Int? = 0,
    @SerialName("createdOn") val createdOn: String? = null,
    @SerialName("dailyCapacity") val dailyCapacity: Int? = 0,
    @SerialName("description") val description: String? = null,
    @SerialName("discountType") val discountType: Int? = 0,
    @SerialName("discountedAmount") val discountedAmount: Double? = 0.0,
    @SerialName("discounttypename") val discounttypename: String? = null,
    val extras: List<Extra> = mutableListOf(),
    @SerialName("id") val id: Int? = 0,
    @SerialName("ingredients") val ingredients: String? = null,
    @SerialName("modifiedBy") val modifiedBy: Int? = -1,
    @SerialName("modifiedOn") val modifiedOn: String? = "",
    @SerialName("name") val name: String? = null,
    @SerialName("optionSets") val optionSets: List<OptionSet>? = emptyList(),
    @SerialName("pic") val pic: String? = null,
    @SerialName("preparationTime") val preparationTime: String? = null,
    @SerialName("price") val price: Double? = 0.0,
    @SerialName("quantity") val quantity: Int? = 0,
    @SerialName("servingSize") val servingSize: String? = null,
    @SerialName("storeId") val storeId: Int? = 0,
    @SerialName("tax") val tax: Double? = 0.0,
    @SerialName("unitId") val unitId: Int? = 0,
    @SerialName("unitName") val unitName: String? = null,
    @SerialName("vat") val vat: Boolean? = false,
    val uuid: String = UUID.randomUUID().toString(),
    @SerialName("courseId") val courseId: String? = null // Course ID for multi-course support
) : Parcelable{
    fun optionsKey() : String{
        var key = ""
        optionSets?.forEach { optionSet ->
            optionSet.options.forEach { option ->
                key += "${optionSet.id}-${option?.id}-${option?.quantity}"
            }
        }
        return key
    }
}