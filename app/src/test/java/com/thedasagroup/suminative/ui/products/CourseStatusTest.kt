package com.thedasagroup.suminative.ui.products

import com.thedasagroup.suminative.data.model.request.order.Cart
import com.thedasagroup.suminative.data.model.request.order.StoreItem
import com.thedasagroup.suminative.ui.products.cart.CourseStatus
import org.junit.Test
import org.junit.Assert.*

/**
 * Unit tests for course status functionality
 */
class CourseStatusTest {

    @Test
    fun `test CourseStatus enum values`() {
        assertEquals("Go", CourseStatus.GO.displayName)
        assertEquals("Preparing", CourseStatus.PREPARING.displayName)
        assertEquals("Complete", CourseStatus.COMPLETE.displayName)
    }

    @Test
    fun `test ProductsScreenState default course statuses`() {
        val state = ProductsScreenState()
        
        // Default state should have empty course statuses
        assertTrue(state.courseStatuses.isEmpty())
    }

    @Test
    fun `test ProductsScreenState with course statuses`() {
        val courseStatuses = mapOf(
            "course_starters" to CourseStatus.GO,
            "course_mains" to CourseStatus.PREPARING,
            "course_desserts" to CourseStatus.COMPLETE
        )
        
        val state = ProductsScreenState(courseStatuses = courseStatuses)
        
        assertEquals(CourseStatus.GO, state.courseStatuses["course_starters"])
        assertEquals(CourseStatus.PREPARING, state.courseStatuses["course_mains"])
        assertEquals(CourseStatus.COMPLETE, state.courseStatuses["course_desserts"])
    }

    @Test
    fun `test course status transitions`() {
        // Test the expected flow: GO -> PREPARING -> COMPLETE
        val initialStatus = CourseStatus.GO
        val preparingStatus = CourseStatus.PREPARING
        val completeStatus = CourseStatus.COMPLETE

        // Verify the flow makes sense
        assertNotEquals(initialStatus, preparingStatus)
        assertNotEquals(preparingStatus, completeStatus)
        assertNotEquals(initialStatus, completeStatus)

        // Verify display names are correct
        assertEquals("Go", initialStatus.displayName)
        assertEquals("Preparing", preparingStatus.displayName)
        assertEquals("Complete", completeStatus.displayName)
    }

    @Test
    fun `test active course tracking`() {
        val state = ProductsScreenState(
            currentActiveCourse = "course_starters",
            availableCourses = listOf(
                MealCourse("course_starters", "Starters", "Starters"),
                MealCourse("course_mains", "Mains", "Mains"),
                MealCourse("course_desserts", "Desserts", "Desserts")
            )
        )

        assertEquals("course_starters", state.currentActiveCourse)
        assertEquals(3, state.availableCourses.size)
    }

    @Test
    fun `test course sequence`() {
        val courses = listOf(
            MealCourse("course_starters", "Starters", "Starters"),
            MealCourse("course_mains", "Mains", "Mains"),
            MealCourse("course_desserts", "Desserts", "Desserts")
        )

        // Test course order
        assertEquals("course_starters", courses[0].id)
        assertEquals("course_mains", courses[1].id)
        assertEquals("course_desserts", courses[2].id)

        // Test finding next course
        val currentIndex = courses.indexOfFirst { it.id == "course_starters" }
        val nextCourse = if (currentIndex < courses.size - 1) courses[currentIndex + 1] else courses[0]
        assertEquals("course_mains", nextCourse.id)
    }

    @Test
    fun `test cart items selection logic`() {
        // Test table selection logic
        val tableId = 101
        val globalCartItems = listOf(
            CartItemWithCourse(
                cart = Cart(quantity = 1, price = 10.0),
                courseId = "course_starters"
            )
        )
        val tableCartItems = mapOf(
            tableId to listOf(
                CartItemWithCourse(
                    cart = Cart(quantity = 2, price = 15.0),
                    courseId = "course_mains"
                )
            )
        )

        // When table is selected, should use table-specific items
        val tableSelectedItems = tableCartItems[tableId]?.filter { it.courseId == "course_mains" } ?: emptyList()
        assertEquals(1, tableSelectedItems.size)
        assertEquals("course_mains", tableSelectedItems[0].courseId)

        // When no table selected, should use global items
        val globalSelectedItems = globalCartItems.filter { it.courseId == "course_starters" }
        assertEquals(1, globalSelectedItems.size)
        assertEquals("course_starters", globalSelectedItems[0].courseId)
    }

    @Test
    fun `test table-based course statuses`() {
        val table1Id = 101
        val table2Id = 102

        val tableCourseStatuses = mapOf(
            table1Id to mapOf(
                "course_starters" to CourseStatus.PREPARING,
                "course_mains" to CourseStatus.GO
            ),
            table2Id to mapOf(
                "course_starters" to CourseStatus.COMPLETE,
                "course_desserts" to CourseStatus.PREPARING
            )
        )

        val state = ProductsScreenState(tableCourseStatuses = tableCourseStatuses)

        // Test table 1 statuses
        assertEquals(CourseStatus.PREPARING, state.tableCourseStatuses[table1Id]?.get("course_starters"))
        assertEquals(CourseStatus.GO, state.tableCourseStatuses[table1Id]?.get("course_mains"))

        // Test table 2 statuses
        assertEquals(CourseStatus.COMPLETE, state.tableCourseStatuses[table2Id]?.get("course_starters"))
        assertEquals(CourseStatus.PREPARING, state.tableCourseStatuses[table2Id]?.get("course_desserts"))

        // Test non-existent course defaults to null (will be handled as GO in getCourseStatus)
        assertNull(state.tableCourseStatuses[table1Id]?.get("course_desserts"))
    }

    @Test
    fun `test table active courses`() {
        val table1Id = 101
        val table2Id = 102

        val tableActiveCourses = mapOf(
            table1Id to "course_mains",
            table2Id to "course_desserts"
        )

        val state = ProductsScreenState(tableActiveCourses = tableActiveCourses)

        assertEquals("course_mains", state.tableActiveCourses[table1Id])
        assertEquals("course_desserts", state.tableActiveCourses[table2Id])
        assertNull(state.tableActiveCourses[999]) // Non-existent table
    }

    @Test
    fun `test table removal logic`() {
        val table1Id = 101
        val table2Id = 102

        val initialTableStatuses = mapOf(
            table1Id to mapOf("course_starters" to CourseStatus.PREPARING),
            table2Id to mapOf("course_mains" to CourseStatus.GO)
        )

        val initialActiveStatuses = mapOf(
            table1Id to "course_starters",
            table2Id to "course_mains"
        )

        // Simulate table removal
        val updatedTableStatuses = initialTableStatuses.toMutableMap()
        updatedTableStatuses.remove(table1Id)

        val updatedActiveStatuses = initialActiveStatuses.toMutableMap()
        updatedActiveStatuses.remove(table1Id)

        // Verify table 1 is removed
        assertFalse(updatedTableStatuses.containsKey(table1Id))
        assertFalse(updatedActiveStatuses.containsKey(table1Id))

        // Verify table 2 remains
        assertTrue(updatedTableStatuses.containsKey(table2Id))
        assertTrue(updatedActiveStatuses.containsKey(table2Id))
        assertEquals(CourseStatus.GO, updatedTableStatuses[table2Id]?.get("course_mains"))
        assertEquals("course_mains", updatedActiveStatuses[table2Id])
    }

    @Test
    fun `test hasGlobalCartItems returns true when global cart has items`() {
        val globalCartItems = listOf(
            Cart(quantity = 1, price = 10.0)
        )
        val globalCartItemsWithCourses = listOf(
            CartItemWithCourse(
                cart = Cart(quantity = 2, price = 15.0),
                courseId = "course_starters"
            )
        )

        val order = Order(carts = globalCartItems)
        val state = ProductsScreenState(
            order = order,
            globalCartItemsWithCourses = globalCartItemsWithCourses,
            selectedTables = emptyList() // No tables selected
        )

        // Should return true when global cart has items and no table selected
        assertTrue(globalCartItems.isNotEmpty() || globalCartItemsWithCourses.isNotEmpty())
    }

    @Test
    fun `test hasGlobalCartItems returns false when table is selected`() {
        val globalCartItems = listOf(
            Cart(quantity = 1, price = 10.0)
        )
        val selectedTables = listOf(
            AreaTableSelectionHelper.AreaTableSelection(
                areaId = 1,
                areaName = "Main Area",
                tableId = 101,
                tableName = "Table 1",
                tableCapacity = 4
            )
        )

        val order = Order(carts = globalCartItems)
        val state = ProductsScreenState(
            order = order,
            selectedTables = selectedTables,
            selectedTableIndex = 0 // Table is selected
        )

        // Should return false when table is selected, regardless of global cart items
        assertTrue(selectedTables.isNotEmpty())
    }

    @Test
    fun `test clearGlobalCart clears all global data`() {
        val initialOrder = Order(
            carts = listOf(Cart(quantity = 1, price = 10.0)),
            netPayable = 10.0,
            tax = 1.0,
            totalPrice = 11.0
        )
        val initialGlobalCartItems = listOf(
            CartItemWithCourse(
                cart = Cart(quantity = 2, price = 15.0),
                courseId = "course_starters"
            )
        )
        val initialCourseStatuses = mapOf(
            "course_starters" to CourseStatus.PREPARING
        )

        val state = ProductsScreenState(
            order = initialOrder,
            globalCartItemsWithCourses = initialGlobalCartItems,
            courseStatuses = initialCourseStatuses,
            currentActiveCourse = "course_mains"
        )

        // Simulate clearing global cart
        val clearedOrder = Order(
            carts = emptyList(),
            netPayable = 0.0,
            tax = 0.0,
            totalPrice = 0.0
        )
        val clearedState = state.copy(
            order = clearedOrder,
            globalCartItemsWithCourses = emptyList(),
            courseStatuses = emptyMap(),
            currentActiveCourse = state.availableCourses.firstOrNull()?.id
        )

        // Verify all global data is cleared
        assertTrue(clearedState.order.carts?.isEmpty() == true)
        assertTrue(clearedState.globalCartItemsWithCourses.isEmpty())
        assertTrue(clearedState.courseStatuses.isEmpty())
        assertEquals("course_starters", clearedState.currentActiveCourse) // Reset to first course
    }

    @Test
    fun `test removeCourse removes course successfully`() {
        val initialCourses = listOf(
            MealCourse("course_starters", "Starters", "Starters"),
            MealCourse("course_mains", "Mains", "Mains"),
            MealCourse("course_desserts", "Desserts", "Desserts")
        )

        val state = ProductsScreenState(availableCourses = initialCourses)

        // Simulate removing a course
        val updatedCourses = initialCourses.toMutableList()
        updatedCourses.removeAll { it.id == "course_mains" }

        val updatedState = state.copy(availableCourses = updatedCourses)

        // Verify course was removed
        assertEquals(2, updatedState.availableCourses.size)
        assertFalse(updatedState.availableCourses.any { it.id == "course_mains" })
        assertTrue(updatedState.availableCourses.any { it.id == "course_starters" })
        assertTrue(updatedState.availableCourses.any { it.id == "course_desserts" })
    }

    @Test
    fun `test removeCourse prevents removing last course`() {
        val singleCourse = listOf(
            MealCourse("course_starters", "Starters", "Starters")
        )

        val state = ProductsScreenState(availableCourses = singleCourse)

        // Should not be able to remove the last course
        assertEquals(1, state.availableCourses.size)

        // Simulate the check that prevents removal
        val canRemove = state.availableCourses.size > 1
        assertFalse(canRemove)
    }

    @Test
    fun `test editCourse updates course name`() {
        val initialCourses = listOf(
            MealCourse("course_starters", "Starters", "Starters"),
            MealCourse("course_mains", "Mains", "Mains"),
            MealCourse("course_desserts", "Desserts", "Desserts")
        )

        val state = ProductsScreenState(availableCourses = initialCourses)

        // Simulate editing a course
        val updatedCourses = initialCourses.map { course ->
            if (course.id == "course_mains") {
                course.copy(name = "Main Courses", displayName = "Main Courses")
            } else {
                course
            }
        }

        val updatedState = state.copy(availableCourses = updatedCourses)

        // Verify course was updated
        val updatedCourse = updatedState.availableCourses.find { it.id == "course_mains" }
        assertNotNull(updatedCourse)
        assertEquals("Main Courses", updatedCourse?.name)
        assertEquals("Main Courses", updatedCourse?.displayName)
    }

    @Test
    fun `test course removal cleans up related data`() {
        val initialCourses = listOf(
            MealCourse("course_starters", "Starters", "Starters"),
            MealCourse("course_mains", "Mains", "Mains"),
            MealCourse("course_desserts", "Desserts", "Desserts")
        )

        val initialCourseStatuses = mapOf(
            "course_starters" to CourseStatus.GO,
            "course_mains" to CourseStatus.PREPARING,
            "course_desserts" to CourseStatus.COMPLETE
        )

        val initialTableCourseStatuses = mapOf(
            101 to mapOf(
                "course_starters" to CourseStatus.GO,
                "course_mains" to CourseStatus.PREPARING
            )
        )

        val initialTableActiveCourses = mapOf(
            101 to "course_mains"
        )

        val state = ProductsScreenState(
            availableCourses = initialCourses,
            courseStatuses = initialCourseStatuses,
            tableCourseStatuses = initialTableCourseStatuses,
            tableActiveCourses = initialTableActiveCourses,
            selectedCourseForNewItems = "course_mains",
            currentActiveCourse = "course_mains"
        )

        // Simulate removing course_mains
        val updatedCourses = initialCourses.toMutableList()
        updatedCourses.removeAll { it.id == "course_mains" }

        val updatedCourseStatuses = initialCourseStatuses.toMutableMap()
        updatedCourseStatuses.remove("course_mains")

        val updatedTableCourseStatuses = initialTableCourseStatuses.mapValues { (_, courseStatuses) ->
            courseStatuses.toMutableMap().apply { remove("course_mains") }
        }

        val updatedTableActiveCourses = initialTableActiveCourses.mapValues { (_, activeCourse) ->
            if (activeCourse == "course_mains") {
                updatedCourses.firstOrNull()?.id ?: "course_starters"
            } else {
                activeCourse
            }
        }

        val updatedState = state.copy(
            availableCourses = updatedCourses,
            courseStatuses = updatedCourseStatuses,
            tableCourseStatuses = updatedTableCourseStatuses,
            tableActiveCourses = updatedTableActiveCourses,
            selectedCourseForNewItems = updatedCourses.firstOrNull()?.id ?: "course_starters",
            currentActiveCourse = updatedCourses.firstOrNull()?.id
        )

        // Verify cleanup
        assertEquals(2, updatedState.availableCourses.size)
        assertFalse(updatedState.courseStatuses.containsKey("course_mains"))
        assertFalse(updatedState.tableCourseStatuses[101]?.containsKey("course_mains") == true)
        assertEquals("course_starters", updatedState.tableActiveCourses[101])
        assertEquals("course_starters", updatedState.selectedCourseForNewItems)
        assertEquals("course_starters", updatedState.currentActiveCourse)
    }

    @Test
    fun `test same item can be added to different courses`() {
        // Create a store item
        val storeItem = StoreItem(
            id = 1,
            name = "Pizza",
            price = 15.99,
            courseId = "course_starters"
        )

        // Create another instance of the same item for a different course
        val storeItemForMains = storeItem.copy(courseId = "course_mains")

        // Create cart items
        val cartItemStarters = Cart(
            storeItem = storeItem,
            quantity = 1,
            price = 15.99,
            netPayable = 15.99
        )

        val cartItemMains = Cart(
            storeItem = storeItemForMains,
            quantity = 2,
            price = 15.99,
            netPayable = 31.98
        )

        // Verify that the items have different course IDs
        assertEquals("course_starters", cartItemStarters.storeItem?.courseId)
        assertEquals("course_mains", cartItemMains.storeItem?.courseId)

        // Verify that they are treated as different items even though they have the same base ID
        assertEquals(1, cartItemStarters.storeItem?.id)
        assertEquals(1, cartItemMains.storeItem?.id)
        assertNotEquals(cartItemStarters.storeItem?.courseId, cartItemMains.storeItem?.courseId)

        // Verify quantities are independent
        assertEquals(1, cartItemStarters.quantity)
        assertEquals(2, cartItemMains.quantity)
    }

    @Test
    fun `test cart item uniqueness with course ID and UUID`() {
        val baseStoreItem = StoreItem(
            id = 1,
            name = "Burger",
            price = 12.50
        )

        // Create cart items for different courses
        val startersItem = Cart(
            storeItem = baseStoreItem.copy(courseId = "course_starters"),
            quantity = 1,
            price = 12.50
        )

        val mainsItem = Cart(
            storeItem = baseStoreItem.copy(courseId = "course_mains"),
            quantity = 3,
            price = 12.50
        )

        val dessertsItem = Cart(
            storeItem = baseStoreItem.copy(courseId = "course_desserts"),
            quantity = 2,
            price = 12.50
        )

        // Verify each cart item has a unique UUID
        assertNotEquals(startersItem.uuid, mainsItem.uuid)
        assertNotEquals(mainsItem.uuid, dessertsItem.uuid)
        assertNotEquals(startersItem.uuid, dessertsItem.uuid)

        // Verify course IDs are different
        assertEquals("course_starters", startersItem.storeItem?.courseId)
        assertEquals("course_mains", mainsItem.storeItem?.courseId)
        assertEquals("course_desserts", dessertsItem.storeItem?.courseId)

        // Verify they all have the same base item ID but different course assignments
        assertEquals(1, startersItem.storeItem?.id)
        assertEquals(1, mainsItem.storeItem?.id)
        assertEquals(1, dessertsItem.storeItem?.id)
    }

    @Test
    fun `test course assignment uses cart UUID for uniqueness`() {
        val storeItem = StoreItem(
            id = 1,
            name = "Pasta",
            price = 18.50
        )

        // Create two cart items with the same store item but different UUIDs
        val cartItem1 = Cart(
            storeItem = storeItem.copy(courseId = "course_starters"),
            quantity = 1,
            price = 18.50
        )

        val cartItem2 = Cart(
            storeItem = storeItem.copy(courseId = "course_mains"),
            quantity = 2,
            price = 18.50
        )

        // Verify they have different UUIDs even though they have the same store item ID
        assertNotEquals(cartItem1.uuid, cartItem2.uuid)
        assertEquals(cartItem1.storeItem?.id, cartItem2.storeItem?.id)
        assertNotEquals(cartItem1.storeItem?.courseId, cartItem2.storeItem?.courseId)

        // This demonstrates that course assignment should use cart UUID, not store item ID
        // to properly handle the same item in different courses
    }

    @Test
    fun `test cart item removal by UUID`() {
        val storeItem1 = StoreItem(
            id = 1,
            name = "Pizza",
            price = 15.99,
            courseId = "course_starters"
        )

        val storeItem2 = StoreItem(
            id = 1,
            name = "Pizza",
            price = 15.99,
            courseId = "course_mains"
        )

        // Create cart items with same store item but different courses
        val cartItem1 = Cart(
            storeItem = storeItem1,
            quantity = 1,
            price = 15.99,
            netPayable = 15.99
        )

        val cartItem2 = Cart(
            storeItem = storeItem2,
            quantity = 2,
            price = 15.99,
            netPayable = 31.98
        )

        // Create a list with both items
        val cartList = mutableListOf(cartItem1, cartItem2)

        // Remove the first item by UUID
        cartList.removeAll { it.uuid == cartItem1.uuid }

        // Verify only the second item remains
        assertEquals(1, cartList.size)
        assertEquals(cartItem2.uuid, cartList[0].uuid)
        assertEquals("course_mains", cartList[0].storeItem?.courseId)
        assertEquals(2, cartList[0].quantity)

        // Verify the removed item is not in the list
        assertFalse(cartList.any { it.uuid == cartItem1.uuid })
    }

    @Test
    fun `test item removal works correctly with multiple same items in different courses`() {
        val storeItem = StoreItem(
            id = 1,
            name = "Burger",
            price = 12.50
        )

        // Create multiple cart items with the same store item but different courses
        val cartItem1 = Cart(
            storeItem = storeItem.copy(courseId = "course_starters"),
            quantity = 1,
            price = 12.50,
            netPayable = 12.50
        )

        val cartItem2 = Cart(
            storeItem = storeItem.copy(courseId = "course_mains"),
            quantity = 2,
            price = 12.50,
            netPayable = 25.00
        )

        val cartItem3 = Cart(
            storeItem = storeItem.copy(courseId = "course_desserts"),
            quantity = 1,
            price = 12.50,
            netPayable = 12.50
        )

        // Create initial cart list
        val initialCartList = mutableListOf(cartItem1, cartItem2, cartItem3)
        assertEquals(3, initialCartList.size)

        // Remove the middle item (cartItem2) using UUID
        initialCartList.removeAll { it.uuid == cartItem2.uuid }

        // Verify only the correct item was removed
        assertEquals(2, initialCartList.size)
        assertTrue(initialCartList.any { it.uuid == cartItem1.uuid })
        assertFalse(initialCartList.any { it.uuid == cartItem2.uuid })
        assertTrue(initialCartList.any { it.uuid == cartItem3.uuid })

        // Verify the remaining items have the correct course assignments
        val remainingItem1 = initialCartList.find { it.uuid == cartItem1.uuid }
        val remainingItem3 = initialCartList.find { it.uuid == cartItem3.uuid }

        assertEquals("course_starters", remainingItem1?.storeItem?.courseId)
        assertEquals("course_desserts", remainingItem3?.storeItem?.courseId)
    }
}
