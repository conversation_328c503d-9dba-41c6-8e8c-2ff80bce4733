# Rewards Retrofit Implementation

This document outlines the conversion of the getUserPoints and getRewardsOverview methods from Ktor to Retrofit in the RewardsRepository.

## Overview

Successfully converted the two new rewards API methods to use Retrofit instead of Ktor, following the same patterns used in other parts of the codebase (like ReservationsRepository).

## Components Created/Modified

### 1. RewardsRetrofitService Interface

**File**: `app/src/main/java/com/thedasagroup/suminative/data/api/RewardsRetrofitService.kt`

```kotlin
interface RewardsRetrofitService {
    
    @GET("BackendDASA-1.0.0/api/rewards/points")
    suspend fun getUserPoints(
        @Query("userId") userId: Int,
        @Query("businessId") businessId: Int
    ): Response<Double>

    @GET("BackendDASA-1.0.0/api/rewards/overview")
    suspend fun getRewardsOverview(
        @Query("userId") userId: Int,
        @Query("businessId") businessId: Int
    ): Response<RewardsOverviewResponse>
}
```

### 2. Updated RewardsRepository

**File**: `app/src/main/java/com/thedasagroup/suminative/data/repo/RewardsRepository.kt`

#### Added Imports
```kotlin
import com.jakewharton.retrofit2.converter.kotlinx.serialization.asConverterFactory
import com.thedasagroup.suminative.data.api.RewardsRetrofitService
import kotlinx.serialization.json.Json
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.OkHttpClient
import okhttp3.logging.HttpLoggingInterceptor
import retrofit2.Retrofit
import timber.log.Timber
```

#### Updated Methods

**getUserPoints() - Now using Retrofit:**
```kotlin
suspend fun getUserPoints(userId: Int, businessId: Int): StateFlow<Async<Double>> {
    val flow = MutableStateFlow<Async<Double>>(Loading())
    withContext(Dispatchers.IO) {
        val response = safeApiCall {
            val service = getRewardsRetrofitService()
            val retrofitResponse = service.getUserPoints(userId, businessId)
            
            if (retrofitResponse.isSuccessful) {
                val points = retrofitResponse.body() ?: 0.0
                Success(points)
            } else {
                Fail(Throwable("Failed to get user points: ${retrofitResponse.message()}"))
            }
        }
        flow.value = response
    }
    return flow
}
```

**getRewardsOverview() - Now using Retrofit:**
```kotlin
suspend fun getRewardsOverview(userId: Int, businessId: Int): StateFlow<Async<RewardsOverviewResponse>> {
    val flow = MutableStateFlow<Async<RewardsOverviewResponse>>(Loading())
    withContext(Dispatchers.IO) {
        val response = safeApiCall {
            val service = getRewardsRetrofitService()
            val retrofitResponse = service.getRewardsOverview(userId, businessId)
            
            if (retrofitResponse.isSuccessful) {
                val overview = retrofitResponse.body()
                if (overview != null) {
                    Success(overview)
                } else {
                    Fail(Throwable("Empty response body for rewards overview"))
                }
            } else {
                Fail(Throwable("Failed to get rewards overview: ${retrofitResponse.message()}"))
            }
        }
        flow.value = response
    }
    return flow
}
```

#### Added Retrofit Service Factory
```kotlin
private fun getRewardsRetrofitService(): RewardsRetrofitService {
    val networkJson = Json { ignoreUnknownKeys = true }
    val logging = HttpLoggingInterceptor(object : HttpLoggingInterceptor.Logger {
        override fun log(message: String) {
            Log.v("Okhttp", message)
            Timber.tag("Okhttp").v(message = message)
        }
    })
    val client: OkHttpClient = OkHttpClient.Builder()
        .addInterceptor(logging)
        .build()
    val retrofit = Retrofit.Builder()
        .baseUrl("$BASE_DOMAIN/")
        .client(client)
        .addConverterFactory(networkJson.asConverterFactory("application/json".toMediaType()))
        .build()
    return retrofit.create(RewardsRetrofitService::class.java)
}
```

## Implementation Details

### Retrofit Configuration
The repository includes a `getRewardsRetrofitService()` method that:
- Sets up OkHttpClient with logging interceptor for debugging
- Configures Retrofit with Kotlinx Serialization JSON converter
- Uses BASE_DOMAIN as base URL
- Returns configured RewardsRetrofitService instance

### Error Handling
- Checks `retrofitResponse.isSuccessful` for HTTP success status
- Returns `Success` with response body on successful requests
- Returns `Fail` with descriptive error messages on failures
- Handles null response bodies gracefully
- Maintains the same error handling patterns as existing code

### API Endpoints
1. **GET /BackendDASA-1.0.0/api/rewards/points**
   - Query parameters: userId, businessId
   - Returns: Double (user points)

2. **GET /BackendDASA-1.0.0/api/rewards/overview**
   - Query parameters: userId, businessId
   - Returns: RewardsOverviewResponse (user points + reward items)

## Benefits of Retrofit Implementation

1. **Type Safety**: Compile-time verification of API endpoints and parameters
2. **Better Error Handling**: More structured error responses and status codes
3. **Consistency**: Follows the same patterns used in other repositories
4. **Debugging**: Better logging and network inspection capabilities
5. **Performance**: Optimized HTTP client with connection pooling
6. **Maintainability**: Cleaner, more readable code structure

## Usage

The API usage remains exactly the same from the ViewModel and Use Case perspective:

```kotlin
// Get user points
viewModel.getUserPoints(userId = 942, businessId = 92)

// Get rewards overview  
viewModel.getRewardsOverview(userId = 942, businessId = 92)
```

The conversion is transparent to the rest of the application - only the underlying HTTP client implementation has changed from Ktor to Retrofit.

## Testing

The existing `RewardsTestActivity` can be used to test both APIs:
1. Enter User ID (942) and Business ID (92)
2. Click "Test Get User Points API" - now uses Retrofit
3. Click "Test Get Rewards Overview API" - now uses Retrofit

Both methods now use Retrofit while maintaining the same functionality and API contracts.
